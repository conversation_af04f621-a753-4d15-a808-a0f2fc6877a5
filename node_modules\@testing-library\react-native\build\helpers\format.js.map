{"version": 3, "file": "format.js", "names": ["_prettyFormat", "_interopRequireWildcard", "require", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "format", "input", "options", "prettyFormat", "plugins", "getCustomPlugin", "mapProps", "ReactElement", "highlight", "printBasicPrototype", "test", "val", "ReactTestComponent", "serialize", "config", "indentation", "depth", "refs", "printer", "newVal", "props", "_default", "exports"], "sources": ["../../src/helpers/format.ts"], "sourcesContent": ["import type { ReactTestRendererJSON } from 'react-test-renderer';\nimport prettyFormat, { NewPlugin, plugins } from 'pretty-format';\n\nexport type MapPropsFunction = (\n  props: Record<string, unknown>,\n  node: ReactTestRendererJSON,\n) => Record<string, unknown>;\n\nexport type FormatOptions = {\n  mapProps?: MapPropsFunction;\n};\n\nconst format = (\n  input: ReactTestRendererJSON | ReactTestRendererJSON[],\n  options: FormatOptions = {},\n) =>\n  prettyFormat(input, {\n    plugins: [getCustomPlugin(options.mapProps), plugins.ReactElement],\n    highlight: true,\n    printBasicPrototype: false,\n  });\n\nconst getCustomPlugin = (mapProps?: MapPropsFunction): NewPlugin => {\n  return {\n    test: (val) => plugins.ReactTestComponent.test(val),\n    serialize: (val, config, indentation, depth, refs, printer) => {\n      let newVal = val;\n      if (mapProps && val.props) {\n        newVal = { ...val, props: mapProps(val.props, val) };\n      }\n      return plugins.ReactTestComponent.serialize(\n        newVal,\n        config,\n        indentation,\n        depth,\n        refs,\n        printer,\n      );\n    },\n  };\n};\n\nexport default format;\n"], "mappings": ";;;;;;AACA,IAAAA,aAAA,GAAAC,uBAAA,CAAAC,OAAA;AAAiE,SAAAC,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAH,wBAAAG,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAWjE,MAAMW,MAAM,GAAGA,CACbC,KAAsD,EACtDC,OAAsB,GAAG,CAAC,CAAC,KAE3B,IAAAC,qBAAY,EAACF,KAAK,EAAE;EAClBG,OAAO,EAAE,CAACC,eAAe,CAACH,OAAO,CAACI,QAAQ,CAAC,EAAEF,qBAAO,CAACG,YAAY,CAAC;EAClEC,SAAS,EAAE,IAAI;EACfC,mBAAmB,EAAE;AACvB,CAAC,CAAC;AAEJ,MAAMJ,eAAe,GAAIC,QAA2B,IAAgB;EAClE,OAAO;IACLI,IAAI,EAAGC,GAAG,IAAKP,qBAAO,CAACQ,kBAAkB,CAACF,IAAI,CAACC,GAAG,CAAC;IACnDE,SAAS,EAAEA,CAACF,GAAG,EAAEG,MAAM,EAAEC,WAAW,EAAEC,KAAK,EAAEC,IAAI,EAAEC,OAAO,KAAK;MAC7D,IAAIC,MAAM,GAAGR,GAAG;MAChB,IAAIL,QAAQ,IAAIK,GAAG,CAACS,KAAK,EAAE;QACzBD,MAAM,GAAG;UAAE,GAAGR,GAAG;UAAES,KAAK,EAAEd,QAAQ,CAACK,GAAG,CAACS,KAAK,EAAET,GAAG;QAAE,CAAC;MACtD;MACA,OAAOP,qBAAO,CAACQ,kBAAkB,CAACC,SAAS,CACzCM,MAAM,EACNL,MAAM,EACNC,WAAW,EACXC,KAAK,EACLC,IAAI,EACJC,OACF,CAAC;IACH;EACF,CAAC;AACH,CAAC;AAAC,IAAAG,QAAA,GAAAC,OAAA,CAAApC,OAAA,GAEac,MAAM", "ignoreList": []}