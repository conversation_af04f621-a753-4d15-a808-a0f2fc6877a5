{"version": 3, "file": "to-be-on-the-screen.js", "names": ["_jestMatcherU<PERSON>s", "require", "_componentTree", "_screen", "_utils", "toBeOnTheScreen", "element", "isNot", "checkHostElement", "pass", "screen", "UNSAFE_root", "getUnsafeRootElement", "errorFound", "formatElement", "errorNotFound", "message", "matcherHint", "RECEIVED_COLOR", "join"], "sources": ["../../src/matchers/to-be-on-the-screen.tsx"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\nimport { matcherHint, RECEIVED_COLOR } from 'jest-matcher-utils';\nimport { getUnsafeRootElement } from '../helpers/component-tree';\nimport { screen } from '../screen';\nimport { checkHostElement, formatElement } from './utils';\n\nexport function toBeOnTheScreen(this: jest.MatcherContext, element: ReactTestInstance) {\n  if (element !== null || !this.isNot) {\n    checkHostElement(element, toBeOnTheScreen, this);\n  }\n\n  const pass = element === null ? false : screen.UNSAFE_root === getUnsafeRootElement(element);\n\n  const errorFound = () => {\n    return `expected element tree not to contain element, but found\\n${formatElement(element)}`;\n  };\n\n  const errorNotFound = () => {\n    return `element could not be found in the element tree`;\n  };\n\n  return {\n    pass,\n    message: () => {\n      return [\n        matcherHint(`${this.isNot ? '.not' : ''}.toBeOnTheScreen`, 'element', ''),\n        '',\n        RECEIVED_COLOR(this.isNot ? errorFound() : errorNotFound()),\n      ].join('\\n');\n    },\n  };\n}\n"], "mappings": ";;;;;;AACA,IAAAA,iBAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AAEO,SAASI,eAAeA,CAA4BC,OAA0B,EAAE;EACrF,IAAIA,OAAO,KAAK,IAAI,IAAI,CAAC,IAAI,CAACC,KAAK,EAAE;IACnC,IAAAC,uBAAgB,EAACF,OAAO,EAAED,eAAe,EAAE,IAAI,CAAC;EAClD;EAEA,MAAMI,IAAI,GAAGH,OAAO,KAAK,IAAI,GAAG,KAAK,GAAGI,cAAM,CAACC,WAAW,KAAK,IAAAC,mCAAoB,EAACN,OAAO,CAAC;EAE5F,MAAMO,UAAU,GAAGA,CAAA,KAAM;IACvB,OAAO,4DAA4D,IAAAC,oBAAa,EAACR,OAAO,CAAC,EAAE;EAC7F,CAAC;EAED,MAAMS,aAAa,GAAGA,CAAA,KAAM;IAC1B,OAAO,gDAAgD;EACzD,CAAC;EAED,OAAO;IACLN,IAAI;IACJO,OAAO,EAAEA,CAAA,KAAM;MACb,OAAO,CACL,IAAAC,6BAAW,EAAC,GAAG,IAAI,CAACV,KAAK,GAAG,MAAM,GAAG,EAAE,kBAAkB,EAAE,SAAS,EAAE,EAAE,CAAC,EACzE,EAAE,EACF,IAAAW,gCAAc,EAAC,IAAI,CAACX,KAAK,GAAGM,UAAU,CAAC,CAAC,GAAGE,aAAa,CAAC,CAAC,CAAC,CAC5D,CAACI,IAAI,CAAC,IAAI,CAAC;IACd;EACF,CAAC;AACH", "ignoreList": []}