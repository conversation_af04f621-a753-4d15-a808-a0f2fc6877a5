{"version": 3, "file": "query-name.js", "names": ["getQueryPrefix", "queryName", "parts", "split"], "sources": ["../../src/helpers/query-name.ts"], "sourcesContent": ["export function getQueryPrefix(queryName: string) {\n  const parts = queryName.split('By');\n  return parts[0];\n}\n"], "mappings": ";;;;;;AAAO,SAASA,cAAcA,CAACC,SAAiB,EAAE;EAChD,MAAMC,KAAK,GAAGD,SAAS,CAACE,KAAK,CAAC,IAAI,CAAC;EACnC,OAAOD,KAAK,CAAC,CAAC,CAAC;AACjB", "ignoreList": []}