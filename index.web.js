import { AppRegistry } from 'react-native';
import App from './App';
import { name as appName } from './app.json';

// Import web-specific styles
import './src/styles/web.css';

// Register the app
AppRegistry.registerComponent(appName, () => App);

// Run the app
AppRegistry.runApplication(appName, {
  initialProps: {},
  rootTag: document.getElementById('app-root'),
});

// Add keyboard navigation hints for web users
if (typeof document !== 'undefined') {
  const hints = document.createElement('div');
  hints.className = 'keyboard-hints';
  hints.innerHTML = '← → Arrow keys to navigate • Space to flip card';
  document.body.appendChild(hints);
}
