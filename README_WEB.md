# LingoLearn - React Native for Web

LingoLearn 现在支持在网页浏览器中运行！这个项目使用 React Native for Web 技术，让同一套代码可以在移动设备和网页上运行。

## 🌟 特性

### 跨平台支持
- ✅ **移动端**: iOS 和 Android 原生应用
- ✅ **Web 端**: 现代浏览器支持
- ✅ **响应式设计**: 自适应不同屏幕尺寸

### Web 专用功能
- 🎹 **键盘导航**: 使用方向键切换单词
- 🔊 **Web Audio API**: 原生音频播放支持
- 💾 **本地存储**: 浏览器 localStorage 集成
- 📋 **剪贴板**: 复制功能支持
- 🖱️ **鼠标交互**: 悬停效果和点击反馈

## 🚀 快速开始

### 安装依赖
```bash
npm install
```

### 运行 Web 版本
```bash
# 开发模式
npm run web

# 构建生产版本
npm run build:web
```

### 运行移动版本
```bash
# Android
npm run android

# iOS
npm run ios
```

## 🎮 Web 端操作指南

### 键盘快捷键
- **← →** 方向键: 切换上一个/下一个单词
- **空格键**: 翻转单词卡片
- **Ctrl/Cmd + F**: 收藏当前单词
- **Ctrl/Cmd + M**: 标记为已掌握

### 鼠标操作
- **点击卡片**: 翻转查看释义
- **点击发音按钮**: 播放单词发音
- **点击收藏/标记按钮**: 管理学习进度

## 🛠️ 技术架构

### 核心技术栈
- **React Native**: 跨平台移动开发框架
- **React Native Web**: Web 平台适配层
- **TypeScript**: 类型安全的 JavaScript
- **Webpack**: Web 端打包工具

### Web 特定技术
- **Web Audio API**: 音频播放
- **localStorage**: 本地数据存储
- **CSS3**: 动画和响应式设计
- **Webpack Dev Server**: 开发服务器

## 📁 项目结构

```
LingoLearn/
├── src/
│   ├── components/          # 可复用组件
│   │   └── WordLearning/   # 单词学习相关组件
│   ├── screens/            # 页面组件
│   ├── services/           # 业务逻辑服务
│   │   ├── wordService.ts  # 单词数据服务
│   │   └── webService.ts   # Web 专用服务
│   ├── types/              # TypeScript 类型定义
│   └── styles/             # 样式文件
│       └── web.css         # Web 专用样式
├── public/                 # Web 静态资源
│   └── index.html         # HTML 模板
├── webpack.config.js       # Webpack 配置
├── index.web.js           # Web 入口文件
└── index.js               # 移动端入口文件
```

## 🌐 浏览器兼容性

### 支持的浏览器
- ✅ Chrome 70+
- ✅ Firefox 65+
- ✅ Safari 12+
- ✅ Edge 79+

### 功能支持
- ✅ ES6+ 语法
- ✅ CSS Grid & Flexbox
- ✅ Web Audio API
- ✅ localStorage
- ✅ 触摸事件 (移动浏览器)

## 🔧 开发指南

### 添加 Web 专用功能
1. 在 `src/services/webService.ts` 中添加新的 Web API 封装
2. 使用 `Platform.OS === 'web'` 进行平台检测
3. 在组件中导入并使用 Web 服务

### 样式适配
- 使用 `Platform.OS` 条件样式
- Web 专用样式放在 `src/styles/web.css`
- 响应式设计使用 CSS 媒体查询

### 调试技巧
- 使用浏览器开发者工具
- React DevTools 扩展
- 网络面板监控 API 调用

## 📱 响应式设计

### 断点设置
- **桌面**: > 768px
- **平板**: 481px - 768px  
- **手机**: ≤ 480px

### 适配策略
- 卡片大小自适应
- 按钮布局响应式调整
- 键盘提示在移动端隐藏

## 🚀 部署

### 构建生产版本
```bash
npm run build:web
```

### 部署到静态托管
生成的 `dist/` 目录可以部署到:
- GitHub Pages
- Netlify
- Vercel
- AWS S3
- 任何静态文件服务器

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

ISC License - 详见 LICENSE 文件
