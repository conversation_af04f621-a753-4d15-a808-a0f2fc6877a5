{"version": 3, "file": "to-be-partially-checked.js", "names": ["_jestMatcherU<PERSON>s", "require", "_accessibility", "_errors", "_utils", "toBePartiallyChecked", "element", "checkHostElement", "hasValidAccessibilityRole", "ErrorWithStack", "pass", "computeAriaChecked", "message", "is", "isNot", "matcherHint", "formatElement", "join", "role", "getRole", "isAccessibilityElement"], "sources": ["../../src/matchers/to-be-partially-checked.tsx"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\nimport { matcherHint } from 'jest-matcher-utils';\nimport { computeAriaChecked, getRole, isAccessibilityElement } from '../helpers/accessibility';\nimport { ErrorWithStack } from '../helpers/errors';\nimport { checkHostElement, formatElement } from './utils';\n\nexport function toBePartiallyChecked(this: jest.MatcherContext, element: ReactTestInstance) {\n  checkHostElement(element, toBePartiallyChecked, this);\n\n  if (!hasValidAccessibilityRole(element)) {\n    throw new ErrorWithStack(\n      'toBePartiallyChecked() works only on accessibility elements with \"checkbox\" role.',\n      toBePartiallyChecked,\n    );\n  }\n\n  return {\n    pass: computeAriaChecked(element) === 'mixed',\n    message: () => {\n      const is = this.isNot ? 'is' : 'is not';\n      return [\n        matcherHint(`${this.isNot ? '.not' : ''}.toBePartiallyChecked`, 'element', ''),\n        '',\n        `Received element ${is} partially checked:`,\n        formatElement(element),\n      ].join('\\n');\n    },\n  };\n}\n\nfunction hasValidAccessibilityRole(element: ReactTestInstance) {\n  const role = getRole(element);\n  return isAccessibilityElement(element) && role === 'checkbox';\n}\n"], "mappings": ";;;;;;AACA,IAAAA,iBAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AAEO,SAASI,oBAAoBA,CAA4BC,OAA0B,EAAE;EAC1F,IAAAC,uBAAgB,EAACD,OAAO,EAAED,oBAAoB,EAAE,IAAI,CAAC;EAErD,IAAI,CAACG,yBAAyB,CAACF,OAAO,CAAC,EAAE;IACvC,MAAM,IAAIG,sBAAc,CACtB,mFAAmF,EACnFJ,oBACF,CAAC;EACH;EAEA,OAAO;IACLK,IAAI,EAAE,IAAAC,iCAAkB,EAACL,OAAO,CAAC,KAAK,OAAO;IAC7CM,OAAO,EAAEA,CAAA,KAAM;MACb,MAAMC,EAAE,GAAG,IAAI,CAACC,KAAK,GAAG,IAAI,GAAG,QAAQ;MACvC,OAAO,CACL,IAAAC,6BAAW,EAAC,GAAG,IAAI,CAACD,KAAK,GAAG,MAAM,GAAG,EAAE,uBAAuB,EAAE,SAAS,EAAE,EAAE,CAAC,EAC9E,EAAE,EACF,oBAAoBD,EAAE,qBAAqB,EAC3C,IAAAG,oBAAa,EAACV,OAAO,CAAC,CACvB,CAACW,IAAI,CAAC,IAAI,CAAC;IACd;EACF,CAAC;AACH;AAEA,SAAST,yBAAyBA,CAACF,OAA0B,EAAE;EAC7D,MAAMY,IAAI,GAAG,IAAAC,sBAAO,EAACb,OAAO,CAAC;EAC7B,OAAO,IAAAc,qCAAsB,EAACd,OAAO,CAAC,IAAIY,IAAI,KAAK,UAAU;AAC/D", "ignoreList": []}