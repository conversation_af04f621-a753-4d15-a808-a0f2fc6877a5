{"version": 3, "file": "host-component-names.js", "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_config", "_renderAct", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "userConfigErrorMessage", "getHostComponentNames", "hostComponentNames", "getConfig", "detectHostComponentNames", "configureInternal", "configureHostComponentNamesIfNeeded", "configHostComponentNames", "renderer", "renderWithAct", "createElement", "View", "Text", "testID", "TextInput", "Image", "Switch", "ScrollView", "Modal", "text", "getByTestId", "root", "type", "textInput", "image", "switch", "scrollView", "modal", "error", "errorMessage", "message", "Error", "instance", "nodes", "findAll", "node", "props", "length", "isHostText", "element", "isHostTextInput", "isHostImage", "isHostSwitch", "isHostScrollView", "isHostModal"], "sources": ["../../src/helpers/host-component-names.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { ReactTestInstance } from 'react-test-renderer';\nimport { Image, Modal, ScrollView, Switch, Text, TextInput, View } from 'react-native';\nimport { configureInternal, getConfig, HostComponentNames } from '../config';\nimport { renderWithAct } from '../render-act';\nimport { HostTestInstance } from './component-tree';\n\nconst userConfigErrorMessage = `There seems to be an issue with your configuration that prevents React Native Testing Library from working correctly.\nPlease check if you are using compatible versions of React Native and React Native Testing Library.`;\n\nexport function getHostComponentNames(): HostComponentNames {\n  let hostComponentNames = getConfig().hostComponentNames;\n  if (!hostComponentNames) {\n    hostComponentNames = detectHostComponentNames();\n    configureInternal({ hostComponentNames });\n  }\n\n  return hostComponentNames;\n}\n\nexport function configureHostComponentNamesIfNeeded() {\n  const configHostComponentNames = getConfig().hostComponentNames;\n  if (configHostComponentNames) {\n    return;\n  }\n\n  const hostComponentNames = detectHostComponentNames();\n  configureInternal({ hostComponentNames });\n}\n\nfunction detectHostComponentNames(): HostComponentNames {\n  try {\n    const renderer = renderWithAct(\n      <View>\n        <Text testID=\"text\">Hello</Text>\n        <TextInput testID=\"textInput\" />\n        <Image testID=\"image\" />\n        <Switch testID=\"switch\" />\n        <ScrollView testID=\"scrollView\" />\n        <Modal testID=\"modal\" />\n      </View>,\n    );\n\n    return {\n      text: getByTestId(renderer.root, 'text').type as string,\n      textInput: getByTestId(renderer.root, 'textInput').type as string,\n      image: getByTestId(renderer.root, 'image').type as string,\n      switch: getByTestId(renderer.root, 'switch').type as string,\n      scrollView: getByTestId(renderer.root, 'scrollView').type as string,\n      modal: getByTestId(renderer.root, 'modal').type as string,\n    };\n  } catch (error) {\n    const errorMessage =\n      error && typeof error === 'object' && 'message' in error ? error.message : null;\n\n    throw new Error(\n      `Trying to detect host component names triggered the following error:\\n\\n${errorMessage}\\n\\n${userConfigErrorMessage}`,\n    );\n  }\n}\n\nfunction getByTestId(instance: ReactTestInstance, testID: string) {\n  const nodes = instance.findAll(\n    (node) => typeof node.type === 'string' && node.props.testID === testID,\n  );\n\n  if (nodes.length === 0) {\n    throw new Error(`Unable to find an element with testID: ${testID}`);\n  }\n\n  return nodes[0];\n}\n\n/**\n * Checks if the given element is a host Text element.\n * @param element The element to check.\n */\nexport function isHostText(element?: ReactTestInstance | null): element is HostTestInstance {\n  return element?.type === getHostComponentNames().text;\n}\n\n/**\n * Checks if the given element is a host TextInput element.\n * @param element The element to check.\n */\nexport function isHostTextInput(element?: ReactTestInstance | null): element is HostTestInstance {\n  return element?.type === getHostComponentNames().textInput;\n}\n\n/**\n * Checks if the given element is a host Image element.\n * @param element The element to check.\n */\nexport function isHostImage(element?: ReactTestInstance | null): element is HostTestInstance {\n  return element?.type === getHostComponentNames().image;\n}\n\n/**\n * Checks if the given element is a host Switch element.\n * @param element The element to check.\n */\nexport function isHostSwitch(element?: ReactTestInstance | null): element is HostTestInstance {\n  return element?.type === getHostComponentNames().switch;\n}\n\n/**\n * Checks if the given element is a host ScrollView element.\n * @param element The element to check.\n */\nexport function isHostScrollView(element?: ReactTestInstance | null): element is HostTestInstance {\n  return element?.type === getHostComponentNames().scrollView;\n}\n\n/**\n * Checks if the given element is a host Modal element.\n * @param element The element to check.\n */\nexport function isHostModal(element?: ReactTestInstance | null): element is HostTestInstance {\n  return element?.type === getHostComponentNames().modal;\n}\n"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AAEA,IAAAC,YAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAF,OAAA;AACA,IAAAG,UAAA,GAAAH,OAAA;AAA8C,SAAAI,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAN,wBAAAM,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAG9C,MAAMW,sBAAsB,GAAG;AAC/B,oGAAoG;AAE7F,SAASC,qBAAqBA,CAAA,EAAuB;EAC1D,IAAIC,kBAAkB,GAAG,IAAAC,iBAAS,EAAC,CAAC,CAACD,kBAAkB;EACvD,IAAI,CAACA,kBAAkB,EAAE;IACvBA,kBAAkB,GAAGE,wBAAwB,CAAC,CAAC;IAC/C,IAAAC,yBAAiB,EAAC;MAAEH;IAAmB,CAAC,CAAC;EAC3C;EAEA,OAAOA,kBAAkB;AAC3B;AAEO,SAASI,mCAAmCA,CAAA,EAAG;EACpD,MAAMC,wBAAwB,GAAG,IAAAJ,iBAAS,EAAC,CAAC,CAACD,kBAAkB;EAC/D,IAAIK,wBAAwB,EAAE;IAC5B;EACF;EAEA,MAAML,kBAAkB,GAAGE,wBAAwB,CAAC,CAAC;EACrD,IAAAC,yBAAiB,EAAC;IAAEH;EAAmB,CAAC,CAAC;AAC3C;AAEA,SAASE,wBAAwBA,CAAA,EAAuB;EACtD,IAAI;IACF,MAAMI,QAAQ,GAAG,IAAAC,wBAAa,eAC5BnC,KAAA,CAAAoC,aAAA,CAACjC,YAAA,CAAAkC,IAAI,qBACHrC,KAAA,CAAAoC,aAAA,CAACjC,YAAA,CAAAmC,IAAI;MAACC,MAAM,EAAC;IAAM,GAAC,OAAW,CAAC,eAChCvC,KAAA,CAAAoC,aAAA,CAACjC,YAAA,CAAAqC,SAAS;MAACD,MAAM,EAAC;IAAW,CAAE,CAAC,eAChCvC,KAAA,CAAAoC,aAAA,CAACjC,YAAA,CAAAsC,KAAK;MAACF,MAAM,EAAC;IAAO,CAAE,CAAC,eACxBvC,KAAA,CAAAoC,aAAA,CAACjC,YAAA,CAAAuC,MAAM;MAACH,MAAM,EAAC;IAAQ,CAAE,CAAC,eAC1BvC,KAAA,CAAAoC,aAAA,CAACjC,YAAA,CAAAwC,UAAU;MAACJ,MAAM,EAAC;IAAY,CAAE,CAAC,eAClCvC,KAAA,CAAAoC,aAAA,CAACjC,YAAA,CAAAyC,KAAK;MAACL,MAAM,EAAC;IAAO,CAAE,CACnB,CACR,CAAC;IAED,OAAO;MACLM,IAAI,EAAEC,WAAW,CAACZ,QAAQ,CAACa,IAAI,EAAE,MAAM,CAAC,CAACC,IAAc;MACvDC,SAAS,EAAEH,WAAW,CAACZ,QAAQ,CAACa,IAAI,EAAE,WAAW,CAAC,CAACC,IAAc;MACjEE,KAAK,EAAEJ,WAAW,CAACZ,QAAQ,CAACa,IAAI,EAAE,OAAO,CAAC,CAACC,IAAc;MACzDG,MAAM,EAAEL,WAAW,CAACZ,QAAQ,CAACa,IAAI,EAAE,QAAQ,CAAC,CAACC,IAAc;MAC3DI,UAAU,EAAEN,WAAW,CAACZ,QAAQ,CAACa,IAAI,EAAE,YAAY,CAAC,CAACC,IAAc;MACnEK,KAAK,EAAEP,WAAW,CAACZ,QAAQ,CAACa,IAAI,EAAE,OAAO,CAAC,CAACC;IAC7C,CAAC;EACH,CAAC,CAAC,OAAOM,KAAK,EAAE;IACd,MAAMC,YAAY,GAChBD,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,SAAS,IAAIA,KAAK,GAAGA,KAAK,CAACE,OAAO,GAAG,IAAI;IAEjF,MAAM,IAAIC,KAAK,CACb,2EAA2EF,YAAY,OAAO7B,sBAAsB,EACtH,CAAC;EACH;AACF;AAEA,SAASoB,WAAWA,CAACY,QAA2B,EAAEnB,MAAc,EAAE;EAChE,MAAMoB,KAAK,GAAGD,QAAQ,CAACE,OAAO,CAC3BC,IAAI,IAAK,OAAOA,IAAI,CAACb,IAAI,KAAK,QAAQ,IAAIa,IAAI,CAACC,KAAK,CAACvB,MAAM,KAAKA,MACnE,CAAC;EAED,IAAIoB,KAAK,CAACI,MAAM,KAAK,CAAC,EAAE;IACtB,MAAM,IAAIN,KAAK,CAAC,0CAA0ClB,MAAM,EAAE,CAAC;EACrE;EAEA,OAAOoB,KAAK,CAAC,CAAC,CAAC;AACjB;;AAEA;AACA;AACA;AACA;AACO,SAASK,UAAUA,CAACC,OAAkC,EAA+B;EAC1F,OAAOA,OAAO,EAAEjB,IAAI,KAAKrB,qBAAqB,CAAC,CAAC,CAACkB,IAAI;AACvD;;AAEA;AACA;AACA;AACA;AACO,SAASqB,eAAeA,CAACD,OAAkC,EAA+B;EAC/F,OAAOA,OAAO,EAAEjB,IAAI,KAAKrB,qBAAqB,CAAC,CAAC,CAACsB,SAAS;AAC5D;;AAEA;AACA;AACA;AACA;AACO,SAASkB,WAAWA,CAACF,OAAkC,EAA+B;EAC3F,OAAOA,OAAO,EAAEjB,IAAI,KAAKrB,qBAAqB,CAAC,CAAC,CAACuB,KAAK;AACxD;;AAEA;AACA;AACA;AACA;AACO,SAASkB,YAAYA,CAACH,OAAkC,EAA+B;EAC5F,OAAOA,OAAO,EAAEjB,IAAI,KAAKrB,qBAAqB,CAAC,CAAC,CAACwB,MAAM;AACzD;;AAEA;AACA;AACA;AACA;AACO,SAASkB,gBAAgBA,CAACJ,OAAkC,EAA+B;EAChG,OAAOA,OAAO,EAAEjB,IAAI,KAAKrB,qBAAqB,CAAC,CAAC,CAACyB,UAAU;AAC7D;;AAEA;AACA;AACA;AACA;AACO,SAASkB,WAAWA,CAACL,OAAkC,EAA+B;EAC3F,OAAOA,OAAO,EAAEjB,IAAI,KAAKrB,qBAAqB,CAAC,CAAC,CAAC0B,KAAK;AACxD", "ignoreList": []}