import React from 'react';
import { TouchableOpacity, Text, StyleSheet, Platform } from 'react-native';
import { webService } from '../../services/webService';

interface PronunciationButtonProps {
  pronunciationUrl: string;
}

const PronunciationButton: React.FC<PronunciationButtonProps> = ({ pronunciationUrl }) => {
  const handlePlay = async () => {
    console.log(`Playing pronunciation from: ${pronunciationUrl}`);

    if (Platform.OS === 'web') {
      try {
        await webService.playAudio(pronunciationUrl);
        console.log('Successfully played pronunciation');
      } catch (error) {
        console.error('Failed to play pronunciation:', error);
        // Fallback: show a message to user
        alert('Unable to play pronunciation. Please check your audio settings.');
      }
    } else {
      // For mobile platforms, use react-native-sound or expo-av
      // This is a placeholder for actual mobile audio implementation
      console.log('Mobile audio playback not implemented yet');
    }
  };

  return (
    <TouchableOpacity
      style={styles.button}
      onPress={handlePlay}
      accessibilityLabel="Play pronunciation"
    >
      <Text style={styles.icon}>🔊</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    backgroundColor: '#3b82f6',
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  icon: {
    fontSize: 20,
    color: 'white',
  },
});

export default PronunciationButton;