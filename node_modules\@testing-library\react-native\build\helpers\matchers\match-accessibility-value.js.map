{"version": 3, "file": "match-accessibility-value.js", "names": ["_accessibility", "require", "_matchStringProp", "matchAccessibilityValue", "node", "matcher", "value", "computeAriaValue", "min", "undefined", "max", "now", "text", "matchStringProp"], "sources": ["../../../src/helpers/matchers/match-accessibility-value.ts"], "sourcesContent": ["import { ReactTestInstance } from 'react-test-renderer';\nimport { computeAriaValue } from '../accessibility';\nimport { TextMatch } from '../../matches';\nimport { matchStringProp } from './match-string-prop';\n\nexport interface AccessibilityValueMatcher {\n  min?: number;\n  max?: number;\n  now?: number;\n  text?: TextMatch;\n}\n\nexport function matchAccessibilityValue(\n  node: ReactTestInstance,\n  matcher: AccessibilityValueMatcher,\n): boolean {\n  const value = computeAriaValue(node);\n  return (\n    (matcher.min === undefined || matcher.min === value?.min) &&\n    (matcher.max === undefined || matcher.max === value?.max) &&\n    (matcher.now === undefined || matcher.now === value?.now) &&\n    (matcher.text === undefined || matchStringProp(value?.text, matcher.text))\n  );\n}\n"], "mappings": ";;;;;;AACA,IAAAA,cAAA,GAAAC,OAAA;AAEA,IAAAC,gBAAA,GAAAD,OAAA;AASO,SAASE,uBAAuBA,CACrCC,IAAuB,EACvBC,OAAkC,EACzB;EACT,MAAMC,KAAK,GAAG,IAAAC,+BAAgB,EAACH,IAAI,CAAC;EACpC,OACE,CAACC,OAAO,CAACG,GAAG,KAAKC,SAAS,IAAIJ,OAAO,CAACG,GAAG,KAAKF,KAAK,EAAEE,GAAG,MACvDH,OAAO,CAACK,GAAG,KAAKD,SAAS,IAAIJ,OAAO,CAACK,GAAG,KAAKJ,KAAK,EAAEI,GAAG,CAAC,KACxDL,OAAO,CAACM,GAAG,KAAKF,SAAS,IAAIJ,OAAO,CAACM,GAAG,KAAKL,KAAK,EAAEK,GAAG,CAAC,KACxDN,OAAO,CAACO,IAAI,KAAKH,SAAS,IAAI,IAAAI,gCAAe,EAACP,KAAK,EAAEM,IAAI,EAAEP,OAAO,CAACO,IAAI,CAAC,CAAC;AAE9E", "ignoreList": []}