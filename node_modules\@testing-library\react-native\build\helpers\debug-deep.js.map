{"version": 3, "file": "debug-deep.js", "names": ["_format", "_interopRequireDefault", "require", "e", "__esModule", "default", "debugDeep", "instance", "options", "message", "formatOptions", "mapProps", "undefined", "console", "log", "format"], "sources": ["../../src/helpers/debug-deep.ts"], "sourcesContent": ["import type { ReactTestRendererJSO<PERSON> } from 'react-test-renderer';\nimport format, { FormatOptions } from './format';\n\nexport type DebugOptions = {\n  message?: string;\n} & FormatOptions;\n\n/**\n * Log pretty-printed deep test component instance\n */\nexport default function debugDeep(\n  instance: ReactTestRendererJSON | ReactTestRendererJSON[],\n  options?: DebugOptions | string,\n) {\n  const message = typeof options === 'string' ? options : options?.message;\n\n  const formatOptions = typeof options === 'object' ? { mapProps: options?.mapProps } : undefined;\n\n  if (message) {\n    // eslint-disable-next-line no-console\n    console.log(`${message}\\n\\n`, format(instance, formatOptions));\n  } else {\n    // eslint-disable-next-line no-console\n    console.log(format(instance, formatOptions));\n  }\n}\n"], "mappings": ";;;;;;AACA,IAAAA,OAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAiD,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAMjD;AACA;AACA;AACe,SAASG,SAASA,CAC/BC,QAAyD,EACzDC,OAA+B,EAC/B;EACA,MAAMC,OAAO,GAAG,OAAOD,OAAO,KAAK,QAAQ,GAAGA,OAAO,GAAGA,OAAO,EAAEC,OAAO;EAExE,MAAMC,aAAa,GAAG,OAAOF,OAAO,KAAK,QAAQ,GAAG;IAAEG,QAAQ,EAAEH,OAAO,EAAEG;EAAS,CAAC,GAAGC,SAAS;EAE/F,IAAIH,OAAO,EAAE;IACX;IACAI,OAAO,CAACC,GAAG,CAAC,GAAGL,OAAO,MAAM,EAAE,IAAAM,eAAM,EAACR,QAAQ,EAAEG,aAAa,CAAC,CAAC;EAChE,CAAC,MAAM;IACL;IACAG,OAAO,CAACC,GAAG,CAAC,IAAAC,eAAM,EAACR,QAAQ,EAAEG,aAAa,CAAC,CAAC;EAC9C;AACF", "ignoreList": []}