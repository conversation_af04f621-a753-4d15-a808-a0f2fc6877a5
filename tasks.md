## Tasks Template

# Tasks Document

- [x] 1. 定义核心数据接口
  - 文件: `src/types/wordLearning.ts`
  - 定义 `Word` 和 `UserWordProgress` TypeScript 接口。
  - 目的: 为单词学习模块的数据结构提供类型安全。
  - _Requirements: 1.1, 3.1_

- [x] 2. 创建 `WordCard` UI 组件
  - 文件: `src/components/WordLearning/WordCard.tsx`
  - 实现单词卡片的基本 UI (正面显示单词，背面显示释义和例句)。
  - 实现卡片点击翻转动画。
  - 目的: 展示单词信息并提供交互。
  - _Leverage: Chakra UI components_
  - _Requirements: 1.1, 1.2_

- [x] 3. 创建 `PronunciationButton` UI 组件
  - 文件: `src/components/WordLearning/PronunciationButton.tsx`
  - 实现发音播放按钮的 UI。
  - 集成音频播放逻辑 (使用 `react-native-sound` 或 `expo-av`)。
  - 目的: 提供单词发音功能。
  - _Leverage: Chakra UI IconButton, audio playback library_
  - _Requirements: 2.1, 2.2_

- [x] 4. 创建 `ActionButtons` UI 组件 (收藏/标记)
  - 文件: `src/components/WordLearning/ActionButtons.tsx`
  - 实现收藏和标记按钮的 UI。
  - 目的: 提供用户对单词的收藏和标记操作。
  - _Leverage: Chakra UI IconButton_
  - _Requirements: 3.1, 3.2_

- [x] 5. 实现 `WordService` (数据服务)
  - 文件: `src/services/wordService.ts`
  - 实现从后端 API 获取单词列表的方法。
  - 实现更新用户单词收藏和标记状态的方法。
  - 目的: 封装单词数据相关的业务逻辑和 API 调用。
  - _Leverage: 后端 API, AsyncStorage_
  - _Requirements: 1.1, 3.1, 3.2_

- [x] 6. 创建 `WordLearningScreen` 页面组件
  - 文件: `src/screens/WordLearningScreen.tsx`
  - 整合 `WordCard`, `PronunciationButton`, `ActionButtons`。
  - 实现单词数据的加载和状态管理。
  - 目的: 单词学习模块的入口页面。
  - _Leverage: `WordService`, `react-swipeable`_
  - _Requirements: 1.1, 4.1_

- [x] 7. 集成 `react-swipeable` 实现卡片切换
  - 文件: `src/screens/WordLearningScreen.tsx` (修改现有文件)
  - 在 `WordLearningScreen` 中集成 `react-swipeable`，实现左右滑动切换单词卡片的功能。
  - 目的: 提供高效的单词切换体验。
  - _Leverage: `react-swipeable`_
  - _Requirements: 4.1, 4.2_

- [x] 8. 实现收藏/标记逻辑与 `WordService` 集成
  - 文件: `src/components/WordLearning/ActionButtons.tsx`, `src/screens/WordLearningScreen.tsx` (修改现有文件)
  - 在 `ActionButtons` 中调用 `WordService` 更新单词的收藏和标记状态。
  - 在 `WordLearningScreen` 中管理单词的收藏和标记状态。
  - 目的: 实现用户对单词的持久化操作。
  - _Leverage: `WordService`_
  - _Requirements: 3.1, 3.2, 3.3_

- [x] 9. 编写 `WordCard`、`PronunciationButton`、`ActionButtons` 单元测试
  - 文件: `src/components/WordLearning/WordCard.test.tsx`, `src/components/WordLearning/PronunciationButton.test.tsx`, `src/components/WordLearning/ActionButtons.test.tsx`
  - 编写组件的渲染、交互和状态逻辑的单元测试。
  - 目的: 确保 UI 组件的质量和可靠性。
  - _Leverage: Jest, React Native Testing Library_
  - _Testing Strategy: Unit Testing_

- [x] 10. 编写 `WordService` 单元测试
  - 文件: `src/services/wordService.test.ts`
  - 编写 `WordService` 的数据获取和状态更新逻辑的单元测试 (模拟 API 调用)。
  - 目的: 确保数据服务层的质量和可靠性。
  - _Leverage: Jest, Mocking libraries_
  - _Testing Strategy: Unit Testing_

- [x] 11. 编写 `WordLearningScreen` 集成测试
  - 文件: `src/screens/WordLearningScreen.test.tsx`
  - 编写 `WordLearningScreen` 的集成测试，测试组件间的交互和数据流。
  - 目的: 确保页面功能的完整性。
  - _Leverage: Jest, React Native Testing Library_
  - _Testing Strategy: Integration Testing_
