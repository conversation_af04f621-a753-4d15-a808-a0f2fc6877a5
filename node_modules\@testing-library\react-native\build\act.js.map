{"version": 3, "file": "act.js", "names": ["_reactTest<PERSON><PERSON><PERSON>", "require", "_reactVersions", "setIsReactActEnvironment", "isReactActEnvironment", "globalThis", "IS_REACT_ACT_ENVIRONMENT", "getIsReactActEnvironment", "withGlobalActEnvironment", "actImplementation", "callback", "previousActEnvironment", "callbackNeedsToBeAwaited", "actResult", "result", "then", "thenable", "resolve", "reject", "returnValue", "error", "act", "checkReactVersionAtLeast", "reactTestRendererAct", "_default", "exports", "default"], "sources": ["../src/act.ts"], "sourcesContent": ["// This file and the act() implementation is sourced from react-testing-library\n// https://github.com/testing-library/react-testing-library/blob/c80809a956b0b9f3289c4a6fa8b5e8cc72d6ef6d/src/act-compat.js\nimport { act as reactTestRendererAct } from 'react-test-renderer';\nimport { checkReactVersionAtLeast } from './react-versions';\n\ntype ReactAct = typeof reactTestRendererAct;\n\n// See https://github.com/reactwg/react-18/discussions/102 for more context on global.IS_REACT_ACT_ENVIRONMENT\ndeclare global {\n  var IS_REACT_ACT_ENVIRONMENT: boolean | undefined;\n}\n\nfunction setIsReactActEnvironment(isReactActEnvironment: boolean | undefined) {\n  globalThis.IS_REACT_ACT_ENVIRONMENT = isReactActEnvironment;\n}\n\nfunction getIsReactActEnvironment() {\n  return globalThis.IS_REACT_ACT_ENVIRONMENT;\n}\n\nfunction withGlobalActEnvironment(actImplementation: ReactAct) {\n  return (callback: Parameters<ReactAct>[0]) => {\n    const previousActEnvironment = getIsReactActEnvironment();\n    setIsReactActEnvironment(true);\n\n    // this code is riddled with eslint disabling comments because this doesn't use real promises but eslint thinks we do\n    try {\n      // The return value of `act` is always a thenable.\n      let callbackNeedsToBeAwaited = false;\n      const actResult = actImplementation(() => {\n        const result = callback();\n        if (\n          result !== null &&\n          typeof result === 'object' &&\n          // @ts-expect-error this should be a promise or thenable\n          // eslint-disable-next-line promise/prefer-await-to-then\n          typeof result.then === 'function'\n        ) {\n          callbackNeedsToBeAwaited = true;\n        }\n        return result;\n      });\n\n      if (callbackNeedsToBeAwaited) {\n        const thenable = actResult;\n        return {\n          then: (resolve: (value: never) => never, reject: (value: never) => never) => {\n            // eslint-disable-next-line\n            thenable.then(\n              // eslint-disable-next-line promise/always-return\n              (returnValue) => {\n                setIsReactActEnvironment(previousActEnvironment);\n                resolve(returnValue);\n              },\n              (error) => {\n                setIsReactActEnvironment(previousActEnvironment);\n                reject(error);\n              },\n            );\n          },\n        };\n      } else {\n        setIsReactActEnvironment(previousActEnvironment);\n        return actResult;\n      }\n    } catch (error) {\n      // Can't be a `finally {}` block since we don't know if we have to immediately restore IS_REACT_ACT_ENVIRONMENT\n      // or if we have to await the callback first.\n      setIsReactActEnvironment(previousActEnvironment);\n      throw error;\n    }\n  };\n}\n\nconst act: ReactAct = checkReactVersionAtLeast(18, 0)\n  ? (withGlobalActEnvironment(reactTestRendererAct) as ReactAct)\n  : reactTestRendererAct;\n\nexport default act;\nexport { setIsReactActEnvironment as setReactActEnvironment, getIsReactActEnvironment };\n"], "mappings": ";;;;;;;;AAEA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAD,OAAA;AAHA;AACA;;AAMA;;AAKA,SAASE,wBAAwBA,CAACC,qBAA0C,EAAE;EAC5EC,UAAU,CAACC,wBAAwB,GAAGF,qBAAqB;AAC7D;AAEA,SAASG,wBAAwBA,CAAA,EAAG;EAClC,OAAOF,UAAU,CAACC,wBAAwB;AAC5C;AAEA,SAASE,wBAAwBA,CAACC,iBAA2B,EAAE;EAC7D,OAAQC,QAAiC,IAAK;IAC5C,MAAMC,sBAAsB,GAAGJ,wBAAwB,CAAC,CAAC;IACzDJ,wBAAwB,CAAC,IAAI,CAAC;;IAE9B;IACA,IAAI;MACF;MACA,IAAIS,wBAAwB,GAAG,KAAK;MACpC,MAAMC,SAAS,GAAGJ,iBAAiB,CAAC,MAAM;QACxC,MAAMK,MAAM,GAAGJ,QAAQ,CAAC,CAAC;QACzB,IACEI,MAAM,KAAK,IAAI,IACf,OAAOA,MAAM,KAAK,QAAQ;QAC1B;QACA;QACA,OAAOA,MAAM,CAACC,IAAI,KAAK,UAAU,EACjC;UACAH,wBAAwB,GAAG,IAAI;QACjC;QACA,OAAOE,MAAM;MACf,CAAC,CAAC;MAEF,IAAIF,wBAAwB,EAAE;QAC5B,MAAMI,QAAQ,GAAGH,SAAS;QAC1B,OAAO;UACLE,IAAI,EAAEA,CAACE,OAAgC,EAAEC,MAA+B,KAAK;YAC3E;YACAF,QAAQ,CAACD,IAAI;YACX;YACCI,WAAW,IAAK;cACfhB,wBAAwB,CAACQ,sBAAsB,CAAC;cAChDM,OAAO,CAACE,WAAW,CAAC;YACtB,CAAC,EACAC,KAAK,IAAK;cACTjB,wBAAwB,CAACQ,sBAAsB,CAAC;cAChDO,MAAM,CAACE,KAAK,CAAC;YACf,CACF,CAAC;UACH;QACF,CAAC;MACH,CAAC,MAAM;QACLjB,wBAAwB,CAACQ,sBAAsB,CAAC;QAChD,OAAOE,SAAS;MAClB;IACF,CAAC,CAAC,OAAOO,KAAK,EAAE;MACd;MACA;MACAjB,wBAAwB,CAACQ,sBAAsB,CAAC;MAChD,MAAMS,KAAK;IACb;EACF,CAAC;AACH;AAEA,MAAMC,GAAa,GAAG,IAAAC,uCAAwB,EAAC,EAAE,EAAE,CAAC,CAAC,GAChDd,wBAAwB,CAACe,sBAAoB,CAAC,GAC/CA,sBAAoB;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEVL,GAAG", "ignoreList": []}