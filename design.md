## Design Template

# Design Document

## Overview

本设计文档详细阐述了 LingoLearn 应用中“单词学习模块”的技术实现方案。该模块旨在提供一个直观、高效的单词学习界面，包括单词卡片展示、发音功能、用户操作（收藏/标记）以及卡片切换等核心功能。设计将遵循模块化原则，确保代码的可维护性、可扩展性和高性能。

## Steering Document Alignment

### Technical Standards (tech.md)
本设计严格遵循 `tech.md` 中定义的技术标准：
-   **核心技术**: 采用 TypeScript/JavaScript 和 React Native 进行开发，利用 Metro Bundler 进行打包。
-   **UI 库**: 使用 Chakra UI 构建用户界面，确保组件的可访问性和一致性。
-   **交互**: 利用 `react-swipeable` 实现卡片滑动切换功能。
-   **数据存储**: 单词数据将从远程后端 API 获取，用户学习进度（收藏、标记）将通过 API 同步至后端，并在本地使用 AsyncStorage 进行缓存和持久化。
-   **性能优化**: 遵循 `tech.md` 中定义的性能优化策略，如使用 `React.memo` 优化组件渲染，对长列表进行虚拟滚动，以及资源懒加载。

### Project Structure (structure.md)
本模块的实现将严格遵循 `structure.md` 中定义的项目组织规范：
-   **目录组织**: 模块相关代码将放置在 `src/screens/WordLearning` 或 `src/components/WordLearning` 目录下，遵循功能分组原则。
-   **命名约定**: 文件、组件、函数和变量将遵循 `PascalCase`、`camelCase` 和 `UPPER_SNAKE_CASE` 等命名约定。
-   **导入模式**: 遵循绝对导入和相对导入的顺序和规范。
-   **代码结构模式**: 组件、函数内部代码将遵循模块化、清晰的组织模式。
-   **模块边界**: 确保 UI 组件与业务逻辑分离，平台特定代码与跨平台代码隔离。

## Code Reuse Analysis

### Existing Components to Leverage
-   **Chakra UI 组件**: Button, Text, Box, Flex, Icon 等基础 UI 组件，用于构建卡片、按钮、文本显示等。
-   **`react-swipeable`**: 用于实现单词卡片的左右滑动切换功能。
-   **通用工具函数**: `src/utils` 下的通用工具函数（例如日期格式化、字符串处理等，如果需要）。

### Integration Points
-   **后端 API**: 
    -   获取单词列表和详细信息。
    -   同步用户对单词的收藏和标记状态。
-   **本地存储 (AsyncStorage)**:
    -   缓存单词数据，提升加载速度和离线可用性。
    -   持久化用户学习进度（例如，当前学习的单词索引、收藏/标记状态）。
-   **全局状态管理**: 如果应用采用全局状态管理方案（例如 Redux, Zustand），单词数据和用户学习状态将集成到全局 Store 中。

## Architecture

本模块将采用组件化和分层架构，确保高内聚、低耦合。

### Modular Design Principles
-   **Single File Responsibility**: 每个文件负责一个单一的关注点，例如 `WordCard.tsx` 负责单词卡片的 UI 和交互。
-   **Component Isolation**: 创建小而专注的组件，例如 `PronunciationButton`、`FavoriteButton`、`MarkButton`。
-   **Service Layer Separation**: 单词数据的获取和用户学习状态的同步将通过独立的 Service 层 (`src/services/wordService.ts`) 进行处理，与 UI 层分离。
-   **Utility Modularity**: 将通用的辅助函数（例如处理发音播放逻辑）封装在独立的工具模块中。

```mermaid
graph TD
    User[用户] --> WordLearningScreen[单词学习页面]
    WordLearningScreen --> WordCard[单词卡片组件]
    WordCard --> PronunciationButton[发音按钮组件]
    WordCard --> ActionButtons[收藏/标记按钮组件]
    WordLearningScreen --> WordService[单词服务]
    WordService --> BackendAPI[后端API]
    WordService --> AsyncStorage[本地存储]
    ActionButtons --> WordService
```

## Components and Interfaces

### Component 1: `WordLearningScreen`
-   **Purpose:** [What this component does]
-   **Interfaces:** [Public methods/APIs]
-   **Dependencies:** [What it depends on]
-   **Reuses:** [Existing components/utilities it builds upon]

### Component 2: `WordCard`
-   **Purpose:** 展示单个单词的卡片，包括正面（单词）和背面（释义、例句）。处理卡片翻转动画。
-   **Interfaces:**
    -   `word: Word` (输入：单词数据对象)
    -   `onFlip: () => void` (输出：卡片翻转事件)
-   **Dependencies:** `PronunciationButton`, `ActionButtons`, Chakra UI 组件。
-   **Reuses:** Chakra UI 的 `Box`, `Text`。

### Component 3: `PronunciationButton`
-   **Purpose:** 提供单词发音播放功能。
-   **Interfaces:**
    -   `pronunciationUrl: string` (输入：发音音频 URL)
    -   `onPlay: () => void` (输出：播放事件)
-   **Dependencies:** 音频播放库 (例如 `react-native-sound` 或 `expo-av`)。
-   **Reuses:** Chakra UI 的 `IconButton`。

### Component 4: `ActionButtons`
-   **Purpose:** 包含收藏和标记单词的按钮。
-   **Interfaces:**
    -   `wordId: string` (输入：单词 ID)
    -   `isFavorited: boolean` (输入：是否已收藏)
    -   `isMastered: boolean` (输入：是否已掌握)
    -   `onFavoriteToggle: (wordId: string) => void` (输出：收藏状态切换事件)
    -   `onMarkToggle: (wordId: string) => void` (输出：标记状态切换事件)
-   **Dependencies:** `WordService` (用于更新状态)。
-   **Reuses:** Chakra UI 的 `IconButton`。

## Data Models

### Model 1: `Word`
```typescript
interface Word {
  id: string;           // 唯一标识符
  text: string;         // 单词文本
  definition: string;   // 单词释义
  pronunciation: string; // 发音音频 URL
  exampleSentence: string; // 例句
}
```

### Model 2: `UserWordProgress`
```typescript
interface UserWordProgress {
  wordId: string;       // 单词 ID
  isFavorited: boolean; // 是否已收藏
  isMastered: boolean;  // 是否已掌握 (例如，标记为“已掌握”或“未掌握”)
  lastReviewedAt: number; // 最后复习时间戳
  reviewCount: number;    // 复习次数
}
```

## Error Handling

### Error Scenarios
1.  **Scenario 1: 单词数据加载失败**
    -   **Handling:** `WordLearningScreen` 在加载单词数据时，如果后端 API 返回错误或网络中断，应显示友好的错误消息和重试按钮。
    -   **User Impact:** 用户无法看到单词卡片，但能收到错误提示并尝试重新加载。
2.  **Scenario 2: 单词发音播放失败**
    -   **Handling:** `PronunciationButton` 在播放发音时，如果音频文件加载失败或播放器错误，应禁用发音按钮并显示提示。
    -   **User Impact:** 用户无法听到发音，但应用不会崩溃。
3.  **Scenario 3: 用户操作同步失败 (收藏/标记)**
    -   **Handling:** `ActionButtons` 在用户点击收藏/标记按钮时，如果同步到后端失败，应显示短暂的错误提示，并允许用户重试。
    -   **User Impact:** 用户操作可能未被保存，但应用仍可继续使用。

## Testing Strategy

### Unit Testing
-   **Unit testing approach**: 使用 Jest 和 React Native Testing Library 对独立的组件和工具函数进行单元测试。
-   **Key components to test**: 
    -   `WordCard` 的渲染和翻转逻辑。
    -   `PronunciationButton` 的播放逻辑。
    -   `ActionButtons` 的点击事件和状态更新。
    -   `WordService` 的数据获取和同步逻辑。

### Integration Testing
-   **Integration testing approach**: 测试组件之间的交互和数据流，例如 `WordLearningScreen` 如何加载和传递数据给 `WordCard`。
-   **Key flows to test**: 
    -   单词学习页面加载并显示第一张卡片。
    -   点击卡片翻转，并成功播放发音。
    -   收藏/标记单词并验证状态更新。

### End-to-End Testing
-   **E2E testing approach**: 使用 Detox 或 Appium 等工具模拟真实用户行为，测试整个应用流程。
-   **User scenarios to test**: 
    -   用户打开应用，进入单词学习页面，学习多个单词，并收藏/标记部分单词。
    -   验证用户学习进度是否正确保存和同步。
    -   测试在不同网络条件下的模块功能。
