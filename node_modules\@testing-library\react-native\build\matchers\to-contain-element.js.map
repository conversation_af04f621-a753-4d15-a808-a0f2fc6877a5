{"version": 3, "file": "to-contain-element.js", "names": ["_jestMatcherU<PERSON>s", "require", "_utils", "toContainElement", "container", "element", "checkHostElement", "matches", "findAll", "node", "pass", "length", "message", "matcherHint", "isNot", "RECEIVED_COLOR", "formatElement", "join"], "sources": ["../../src/matchers/to-contain-element.tsx"], "sourcesContent": ["import { ReactTestInstance } from 'react-test-renderer';\nimport { matcherHint, RECEIVED_COLOR } from 'jest-matcher-utils';\nimport { checkHostElement, formatElement } from './utils';\n\nexport function toContainElement(\n  this: jest.MatcherContext,\n  container: ReactTestInstance,\n  element: ReactTestInstance | null,\n) {\n  checkHostElement(container, toContainElement, this);\n\n  if (element !== null) {\n    checkHostElement(element, toContainElement, this);\n  }\n\n  let matches: ReactTestInstance[] = [];\n  if (element) {\n    matches = container.findAll((node) => node === element);\n  }\n\n  return {\n    pass: matches.length > 0,\n    message: () => {\n      return [\n        matcherHint(`${this.isNot ? '.not' : ''}.toContainElement`, 'container', 'element'),\n        '',\n        RECEIVED_COLOR(`${formatElement(container)} ${\n          this.isNot ? '\\n\\ncontains:\\n\\n' : '\\n\\ndoes not contain:\\n\\n'\n        } ${formatElement(element)}\n        `),\n      ].join('\\n');\n    },\n  };\n}\n"], "mappings": ";;;;;;AACA,IAAAA,iBAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AAEO,SAASE,gBAAgBA,CAE9BC,SAA4B,EAC5BC,OAAiC,EACjC;EACA,IAAAC,uBAAgB,EAACF,SAAS,EAAED,gBAAgB,EAAE,IAAI,CAAC;EAEnD,IAAIE,OAAO,KAAK,IAAI,EAAE;IACpB,IAAAC,uBAAgB,EAACD,OAAO,EAAEF,gBAAgB,EAAE,IAAI,CAAC;EACnD;EAEA,IAAII,OAA4B,GAAG,EAAE;EACrC,IAAIF,OAAO,EAAE;IACXE,OAAO,GAAGH,SAAS,CAACI,OAAO,CAAEC,IAAI,IAAKA,IAAI,KAAKJ,OAAO,CAAC;EACzD;EAEA,OAAO;IACLK,IAAI,EAAEH,OAAO,CAACI,MAAM,GAAG,CAAC;IACxBC,OAAO,EAAEA,CAAA,KAAM;MACb,OAAO,CACL,IAAAC,6BAAW,EAAC,GAAG,IAAI,CAACC,KAAK,GAAG,MAAM,GAAG,EAAE,mBAAmB,EAAE,WAAW,EAAE,SAAS,CAAC,EACnF,EAAE,EACF,IAAAC,gCAAc,EAAC,GAAG,IAAAC,oBAAa,EAACZ,SAAS,CAAC,IACxC,IAAI,CAACU,KAAK,GAAG,mBAAmB,GAAG,2BAA2B,IAC5D,IAAAE,oBAAa,EAACX,OAAO,CAAC;AAClC,SAAS,CAAC,CACH,CAACY,IAAI,CAAC,IAAI,CAAC;IACd;EACF,CAAC;AACH", "ignoreList": []}