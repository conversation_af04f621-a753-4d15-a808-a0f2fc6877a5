{"version": 3, "file": "flush-micro-tasks.js", "names": ["_timers", "require", "flushMicroTasks", "Promise", "resolve", "setImmediate", "flushMicroTasksLegacy", "then"], "sources": ["../src/flush-micro-tasks.ts"], "sourcesContent": ["import { setImmediate } from './helpers/timers';\n\nexport function flushMicroTasks() {\n  return new Promise((resolve) => setImmediate(resolve));\n}\n\n/**\n * @deprecated To be removed in the next major release.\n */\ntype Thenable<T> = { then: (callback: () => T) => unknown };\n\n/**\n * This legacy implementation of `flushMicroTasks` is used for compatibility with\n * older versions of React Native (pre 0.71) which uses Promise polyfil.\n *\n * For users with older version of React Native there is a workaround of using our own\n * Jest preset instead the `react-native` one, but requiring such change would be a\n * breaking change for existing users.\n *\n * @deprecated To be removed in the next major release.\n */\nexport function flushMicroTasksLegacy(): Thenable<void> {\n  return {\n    // using \"thenable\" instead of a Promise, because otherwise it breaks when\n    // using \"modern\" fake timers\n    then(resolve) {\n      setImmediate(resolve);\n    },\n  };\n}\n"], "mappings": ";;;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AAEO,SAASC,eAAeA,CAAA,EAAG;EAChC,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAK,IAAAC,oBAAY,EAACD,OAAO,CAAC,CAAC;AACxD;;AAEA;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASE,qBAAqBA,CAAA,EAAmB;EACtD,OAAO;IACL;IACA;IACAC,IAAIA,CAACH,OAAO,EAAE;MACZ,IAAAC,oBAAY,EAACD,OAAO,CAAC;IACvB;EACF,CAAC;AACH", "ignoreList": []}