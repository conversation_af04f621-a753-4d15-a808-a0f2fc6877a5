{"version": 3, "file": "accessibility.js", "names": ["_reactNative", "require", "_componentTree", "_hostComponentNames", "_textContent", "_textInput", "accessibilityStateKeys", "exports", "accessibilityValueKeys", "isHiddenFromAccessibility", "element", "cache", "current", "isCurrentSubtreeInaccessible", "get", "undefined", "isSubtreeInaccessible", "set", "parent", "isInaccessible", "props", "accessibilityElementsHidden", "importantForAccessibility", "flatStyle", "StyleSheet", "flatten", "style", "display", "hostSiblings", "getHostSiblings", "some", "sibling", "computeAriaModal", "isAccessibilityElement", "isHostImage", "alt", "accessible", "hostComponentNames", "getHostComponentNames", "type", "text", "textInput", "switch", "getRole", "explicitRole", "role", "accessibilityRole", "normalizeRole", "isHostText", "accessibilityViewIsModal", "computeAriaLabel", "<PERSON><PERSON><PERSON><PERSON>", "accessibilityLabel", "computeAriaLabelledBy", "accessibilityLabelledBy", "computeAriaBusy", "accessibilityState", "busy", "computeAriaChecked", "isHostSwitch", "value", "rolesSupportingCheckedState", "checked", "computeAriaDisabled", "isHostTextInput", "isTextInputEditable", "disabled", "computeAriaExpanded", "expanded", "computeAriaSelected", "selected", "computeAriaValue", "accessibilityValue", "ariaValueMax", "ariaValueMin", "ariaValueNow", "ariaValueText", "max", "min", "now", "computeAccessibleName", "label", "labelElementId", "rootElement", "getUnsafeRootElement", "labelElement", "findByProps", "nativeID", "getTextContent", "checkbox", "radio"], "sources": ["../../src/helpers/accessibility.ts"], "sourcesContent": ["import {\n  AccessibilityRole,\n  AccessibilityState,\n  AccessibilityValue,\n  Role,\n  StyleSheet,\n} from 'react-native';\nimport { ReactTestInstance } from 'react-test-renderer';\nimport { getHostSiblings, getUnsafeRootElement } from './component-tree';\nimport {\n  getHostComponentNames,\n  isHostImage,\n  isHostSwitch,\n  isHostText,\n  isHostTextInput,\n} from './host-component-names';\nimport { getTextContent } from './text-content';\nimport { isTextInputEditable } from './text-input';\n\ntype IsInaccessibleOptions = {\n  cache?: WeakMap<ReactTestInstance, boolean>;\n};\n\nexport const accessibilityStateKeys: (keyof AccessibilityState)[] = [\n  'disabled',\n  'selected',\n  'checked',\n  'busy',\n  'expanded',\n];\n\nexport const accessibilityValueKeys: (keyof AccessibilityValue)[] = ['min', 'max', 'now', 'text'];\n\nexport function isHiddenFromAccessibility(\n  element: ReactTestInstance | null,\n  { cache }: IsInaccessibleOptions = {},\n): boolean {\n  if (element == null) {\n    return true;\n  }\n\n  let current: ReactTestInstance | null = element;\n  while (current) {\n    let isCurrentSubtreeInaccessible = cache?.get(current);\n\n    if (isCurrentSubtreeInaccessible === undefined) {\n      isCurrentSubtreeInaccessible = isSubtreeInaccessible(current);\n      cache?.set(current, isCurrentSubtreeInaccessible);\n    }\n\n    if (isCurrentSubtreeInaccessible) {\n      return true;\n    }\n\n    current = current.parent;\n  }\n\n  return false;\n}\n\n/** RTL-compatibility alias for `isHiddenFromAccessibility` */\nexport const isInaccessible = isHiddenFromAccessibility;\n\nfunction isSubtreeInaccessible(element: ReactTestInstance): boolean {\n  // Null props can happen for React.Fragments\n  if (element.props == null) {\n    return false;\n  }\n\n  // See: https://reactnative.dev/docs/accessibility#aria-hidden\n  if (element.props['aria-hidden']) {\n    return true;\n  }\n\n  // iOS: accessibilityElementsHidden\n  // See: https://reactnative.dev/docs/accessibility#accessibilityelementshidden-ios\n  if (element.props.accessibilityElementsHidden) {\n    return true;\n  }\n\n  // Android: importantForAccessibility\n  // See: https://reactnative.dev/docs/accessibility#importantforaccessibility-android\n  if (element.props.importantForAccessibility === 'no-hide-descendants') {\n    return true;\n  }\n\n  // Note that `opacity: 0` is not treated as inaccessible on iOS\n  const flatStyle = StyleSheet.flatten(element.props.style) ?? {};\n  if (flatStyle.display === 'none') return true;\n\n  // iOS: accessibilityViewIsModal or aria-modal\n  // See: https://reactnative.dev/docs/accessibility#accessibilityviewismodal-ios\n  const hostSiblings = getHostSiblings(element);\n  if (hostSiblings.some((sibling) => computeAriaModal(sibling))) {\n    return true;\n  }\n\n  return false;\n}\n\nexport function isAccessibilityElement(element: ReactTestInstance | null): boolean {\n  if (element == null) {\n    return false;\n  }\n\n  // https://github.com/facebook/react-native/blob/8dabed60f456e76a9e53273b601446f34de41fb5/packages/react-native/Libraries/Image/Image.ios.js#L172\n  if (isHostImage(element) && element.props.alt !== undefined) {\n    return true;\n  }\n\n  if (element.props.accessible !== undefined) {\n    return element.props.accessible;\n  }\n\n  const hostComponentNames = getHostComponentNames();\n  return (\n    element?.type === hostComponentNames?.text ||\n    element?.type === hostComponentNames?.textInput ||\n    element?.type === hostComponentNames?.switch\n  );\n}\n\n/**\n * Returns the accessibility role for given element. It will return explicit\n * role from either `role` or `accessibilityRole` props if set.\n *\n * If explicit role is not available, it would try to return default element\n * role:\n * - `text` for `Text` elements\n *\n * In all other cases this functions returns `none`.\n *\n * @param element\n * @returns\n */\nexport function getRole(element: ReactTestInstance): Role | AccessibilityRole {\n  const explicitRole = element.props.role ?? element.props.accessibilityRole;\n  if (explicitRole) {\n    return normalizeRole(explicitRole);\n  }\n\n  if (isHostText(element)) {\n    return 'text';\n  }\n\n  // Note: host Image elements report \"image\" role in screen reader only on Android, but not on iOS.\n  // It's better to require explicit role for Image elements.\n\n  return 'none';\n}\n\n/**\n * There are some duplications between (ARIA) `Role` and `AccessibilityRole` types.\n * Resolve them by using ARIA `Role` type where possible.\n *\n * @param role Role to normalize\n * @returns Normalized role\n */\nexport function normalizeRole(role: string): Role | AccessibilityRole {\n  if (role === 'image') {\n    return 'img';\n  }\n\n  return role as Role | AccessibilityRole;\n}\n\nexport function computeAriaModal(element: ReactTestInstance): boolean | undefined {\n  return element.props['aria-modal'] ?? element.props.accessibilityViewIsModal;\n}\n\nexport function computeAriaLabel(element: ReactTestInstance): string | undefined {\n  const explicitLabel = element.props['aria-label'] ?? element.props.accessibilityLabel;\n  if (explicitLabel) {\n    return explicitLabel;\n  }\n\n  //https://github.com/facebook/react-native/blob/8dabed60f456e76a9e53273b601446f34de41fb5/packages/react-native/Libraries/Image/Image.ios.js#L173\n  if (isHostImage(element) && element.props.alt) {\n    return element.props.alt;\n  }\n\n  return undefined;\n}\n\nexport function computeAriaLabelledBy(element: ReactTestInstance): string | undefined {\n  return element.props['aria-labelledby'] ?? element.props.accessibilityLabelledBy;\n}\n\n// See: https://github.com/callstack/react-native-testing-library/wiki/Accessibility:-State#busy-state\nexport function computeAriaBusy({ props }: ReactTestInstance): boolean {\n  return props['aria-busy'] ?? props.accessibilityState?.busy ?? false;\n}\n\n// See: https://github.com/callstack/react-native-testing-library/wiki/Accessibility:-State#checked-state\nexport function computeAriaChecked(element: ReactTestInstance): AccessibilityState['checked'] {\n  const { props } = element;\n\n  if (isHostSwitch(element)) {\n    return props.value;\n  }\n\n  const role = getRole(element);\n  if (!rolesSupportingCheckedState[role]) {\n    return undefined;\n  }\n\n  return props['aria-checked'] ?? props.accessibilityState?.checked;\n}\n\n// See: https://github.com/callstack/react-native-testing-library/wiki/Accessibility:-State#disabled-state\nexport function computeAriaDisabled(element: ReactTestInstance): boolean {\n  if (isHostTextInput(element) && !isTextInputEditable(element)) {\n    return true;\n  }\n\n  const { props } = element;\n  return props['aria-disabled'] ?? props.accessibilityState?.disabled ?? false;\n}\n\n// See: https://github.com/callstack/react-native-testing-library/wiki/Accessibility:-State#expanded-state\nexport function computeAriaExpanded({ props }: ReactTestInstance): boolean | undefined {\n  return props['aria-expanded'] ?? props.accessibilityState?.expanded;\n}\n\n// See: https://github.com/callstack/react-native-testing-library/wiki/Accessibility:-State#selected-state\nexport function computeAriaSelected({ props }: ReactTestInstance): boolean {\n  return props['aria-selected'] ?? props.accessibilityState?.selected ?? false;\n}\n\nexport function computeAriaValue(element: ReactTestInstance): AccessibilityValue {\n  const {\n    accessibilityValue,\n    'aria-valuemax': ariaValueMax,\n    'aria-valuemin': ariaValueMin,\n    'aria-valuenow': ariaValueNow,\n    'aria-valuetext': ariaValueText,\n  } = element.props;\n\n  return {\n    max: ariaValueMax ?? accessibilityValue?.max,\n    min: ariaValueMin ?? accessibilityValue?.min,\n    now: ariaValueNow ?? accessibilityValue?.now,\n    text: ariaValueText ?? accessibilityValue?.text,\n  };\n}\n\nexport function computeAccessibleName(element: ReactTestInstance): string | undefined {\n  const label = computeAriaLabel(element);\n  if (label) {\n    return label;\n  }\n\n  const labelElementId = computeAriaLabelledBy(element);\n  if (labelElementId) {\n    const rootElement = getUnsafeRootElement(element);\n    const labelElement = rootElement?.findByProps({ nativeID: labelElementId });\n    if (labelElement) {\n      return getTextContent(labelElement);\n    }\n  }\n\n  return getTextContent(element);\n}\n\ntype RoleSupportMap = Partial<Record<Role | AccessibilityRole, true>>;\n\nexport const rolesSupportingCheckedState: RoleSupportMap = {\n  checkbox: true,\n  radio: true,\n  switch: true,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAQA,IAAAC,cAAA,GAAAD,OAAA;AACA,IAAAE,mBAAA,GAAAF,OAAA;AAOA,IAAAG,YAAA,GAAAH,OAAA;AACA,IAAAI,UAAA,GAAAJ,OAAA;AAMO,MAAMK,sBAAoD,GAAAC,OAAA,CAAAD,sBAAA,GAAG,CAClE,UAAU,EACV,UAAU,EACV,SAAS,EACT,MAAM,EACN,UAAU,CACX;AAEM,MAAME,sBAAoD,GAAAD,OAAA,CAAAC,sBAAA,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;AAE1F,SAASC,yBAAyBA,CACvCC,OAAiC,EACjC;EAAEC;AAA6B,CAAC,GAAG,CAAC,CAAC,EAC5B;EACT,IAAID,OAAO,IAAI,IAAI,EAAE;IACnB,OAAO,IAAI;EACb;EAEA,IAAIE,OAAiC,GAAGF,OAAO;EAC/C,OAAOE,OAAO,EAAE;IACd,IAAIC,4BAA4B,GAAGF,KAAK,EAAEG,GAAG,CAACF,OAAO,CAAC;IAEtD,IAAIC,4BAA4B,KAAKE,SAAS,EAAE;MAC9CF,4BAA4B,GAAGG,qBAAqB,CAACJ,OAAO,CAAC;MAC7DD,KAAK,EAAEM,GAAG,CAACL,OAAO,EAAEC,4BAA4B,CAAC;IACnD;IAEA,IAAIA,4BAA4B,EAAE;MAChC,OAAO,IAAI;IACb;IAEAD,OAAO,GAAGA,OAAO,CAACM,MAAM;EAC1B;EAEA,OAAO,KAAK;AACd;;AAEA;AACO,MAAMC,cAAc,GAAAZ,OAAA,CAAAY,cAAA,GAAGV,yBAAyB;AAEvD,SAASO,qBAAqBA,CAACN,OAA0B,EAAW;EAClE;EACA,IAAIA,OAAO,CAACU,KAAK,IAAI,IAAI,EAAE;IACzB,OAAO,KAAK;EACd;;EAEA;EACA,IAAIV,OAAO,CAACU,KAAK,CAAC,aAAa,CAAC,EAAE;IAChC,OAAO,IAAI;EACb;;EAEA;EACA;EACA,IAAIV,OAAO,CAACU,KAAK,CAACC,2BAA2B,EAAE;IAC7C,OAAO,IAAI;EACb;;EAEA;EACA;EACA,IAAIX,OAAO,CAACU,KAAK,CAACE,yBAAyB,KAAK,qBAAqB,EAAE;IACrE,OAAO,IAAI;EACb;;EAEA;EACA,MAAMC,SAAS,GAAGC,uBAAU,CAACC,OAAO,CAACf,OAAO,CAACU,KAAK,CAACM,KAAK,CAAC,IAAI,CAAC,CAAC;EAC/D,IAAIH,SAAS,CAACI,OAAO,KAAK,MAAM,EAAE,OAAO,IAAI;;EAE7C;EACA;EACA,MAAMC,YAAY,GAAG,IAAAC,8BAAe,EAACnB,OAAO,CAAC;EAC7C,IAAIkB,YAAY,CAACE,IAAI,CAAEC,OAAO,IAAKC,gBAAgB,CAACD,OAAO,CAAC,CAAC,EAAE;IAC7D,OAAO,IAAI;EACb;EAEA,OAAO,KAAK;AACd;AAEO,SAASE,sBAAsBA,CAACvB,OAAiC,EAAW;EACjF,IAAIA,OAAO,IAAI,IAAI,EAAE;IACnB,OAAO,KAAK;EACd;;EAEA;EACA,IAAI,IAAAwB,+BAAW,EAACxB,OAAO,CAAC,IAAIA,OAAO,CAACU,KAAK,CAACe,GAAG,KAAKpB,SAAS,EAAE;IAC3D,OAAO,IAAI;EACb;EAEA,IAAIL,OAAO,CAACU,KAAK,CAACgB,UAAU,KAAKrB,SAAS,EAAE;IAC1C,OAAOL,OAAO,CAACU,KAAK,CAACgB,UAAU;EACjC;EAEA,MAAMC,kBAAkB,GAAG,IAAAC,yCAAqB,EAAC,CAAC;EAClD,OACE5B,OAAO,EAAE6B,IAAI,KAAKF,kBAAkB,EAAEG,IAAI,IAC1C9B,OAAO,EAAE6B,IAAI,KAAKF,kBAAkB,EAAEI,SAAS,IAC/C/B,OAAO,EAAE6B,IAAI,KAAKF,kBAAkB,EAAEK,MAAM;AAEhD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,OAAOA,CAACjC,OAA0B,EAA4B;EAC5E,MAAMkC,YAAY,GAAGlC,OAAO,CAACU,KAAK,CAACyB,IAAI,IAAInC,OAAO,CAACU,KAAK,CAAC0B,iBAAiB;EAC1E,IAAIF,YAAY,EAAE;IAChB,OAAOG,aAAa,CAACH,YAAY,CAAC;EACpC;EAEA,IAAI,IAAAI,8BAAU,EAACtC,OAAO,CAAC,EAAE;IACvB,OAAO,MAAM;EACf;;EAEA;EACA;;EAEA,OAAO,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASqC,aAAaA,CAACF,IAAY,EAA4B;EACpE,IAAIA,IAAI,KAAK,OAAO,EAAE;IACpB,OAAO,KAAK;EACd;EAEA,OAAOA,IAAI;AACb;AAEO,SAASb,gBAAgBA,CAACtB,OAA0B,EAAuB;EAChF,OAAOA,OAAO,CAACU,KAAK,CAAC,YAAY,CAAC,IAAIV,OAAO,CAACU,KAAK,CAAC6B,wBAAwB;AAC9E;AAEO,SAASC,gBAAgBA,CAACxC,OAA0B,EAAsB;EAC/E,MAAMyC,aAAa,GAAGzC,OAAO,CAACU,KAAK,CAAC,YAAY,CAAC,IAAIV,OAAO,CAACU,KAAK,CAACgC,kBAAkB;EACrF,IAAID,aAAa,EAAE;IACjB,OAAOA,aAAa;EACtB;;EAEA;EACA,IAAI,IAAAjB,+BAAW,EAACxB,OAAO,CAAC,IAAIA,OAAO,CAACU,KAAK,CAACe,GAAG,EAAE;IAC7C,OAAOzB,OAAO,CAACU,KAAK,CAACe,GAAG;EAC1B;EAEA,OAAOpB,SAAS;AAClB;AAEO,SAASsC,qBAAqBA,CAAC3C,OAA0B,EAAsB;EACpF,OAAOA,OAAO,CAACU,KAAK,CAAC,iBAAiB,CAAC,IAAIV,OAAO,CAACU,KAAK,CAACkC,uBAAuB;AAClF;;AAEA;AACO,SAASC,eAAeA,CAAC;EAAEnC;AAAyB,CAAC,EAAW;EACrE,OAAOA,KAAK,CAAC,WAAW,CAAC,IAAIA,KAAK,CAACoC,kBAAkB,EAAEC,IAAI,IAAI,KAAK;AACtE;;AAEA;AACO,SAASC,kBAAkBA,CAAChD,OAA0B,EAAiC;EAC5F,MAAM;IAAEU;EAAM,CAAC,GAAGV,OAAO;EAEzB,IAAI,IAAAiD,gCAAY,EAACjD,OAAO,CAAC,EAAE;IACzB,OAAOU,KAAK,CAACwC,KAAK;EACpB;EAEA,MAAMf,IAAI,GAAGF,OAAO,CAACjC,OAAO,CAAC;EAC7B,IAAI,CAACmD,2BAA2B,CAAChB,IAAI,CAAC,EAAE;IACtC,OAAO9B,SAAS;EAClB;EAEA,OAAOK,KAAK,CAAC,cAAc,CAAC,IAAIA,KAAK,CAACoC,kBAAkB,EAAEM,OAAO;AACnE;;AAEA;AACO,SAASC,mBAAmBA,CAACrD,OAA0B,EAAW;EACvE,IAAI,IAAAsD,mCAAe,EAACtD,OAAO,CAAC,IAAI,CAAC,IAAAuD,8BAAmB,EAACvD,OAAO,CAAC,EAAE;IAC7D,OAAO,IAAI;EACb;EAEA,MAAM;IAAEU;EAAM,CAAC,GAAGV,OAAO;EACzB,OAAOU,KAAK,CAAC,eAAe,CAAC,IAAIA,KAAK,CAACoC,kBAAkB,EAAEU,QAAQ,IAAI,KAAK;AAC9E;;AAEA;AACO,SAASC,mBAAmBA,CAAC;EAAE/C;AAAyB,CAAC,EAAuB;EACrF,OAAOA,KAAK,CAAC,eAAe,CAAC,IAAIA,KAAK,CAACoC,kBAAkB,EAAEY,QAAQ;AACrE;;AAEA;AACO,SAASC,mBAAmBA,CAAC;EAAEjD;AAAyB,CAAC,EAAW;EACzE,OAAOA,KAAK,CAAC,eAAe,CAAC,IAAIA,KAAK,CAACoC,kBAAkB,EAAEc,QAAQ,IAAI,KAAK;AAC9E;AAEO,SAASC,gBAAgBA,CAAC7D,OAA0B,EAAsB;EAC/E,MAAM;IACJ8D,kBAAkB;IAClB,eAAe,EAAEC,YAAY;IAC7B,eAAe,EAAEC,YAAY;IAC7B,eAAe,EAAEC,YAAY;IAC7B,gBAAgB,EAAEC;EACpB,CAAC,GAAGlE,OAAO,CAACU,KAAK;EAEjB,OAAO;IACLyD,GAAG,EAAEJ,YAAY,IAAID,kBAAkB,EAAEK,GAAG;IAC5CC,GAAG,EAAEJ,YAAY,IAAIF,kBAAkB,EAAEM,GAAG;IAC5CC,GAAG,EAAEJ,YAAY,IAAIH,kBAAkB,EAAEO,GAAG;IAC5CvC,IAAI,EAAEoC,aAAa,IAAIJ,kBAAkB,EAAEhC;EAC7C,CAAC;AACH;AAEO,SAASwC,qBAAqBA,CAACtE,OAA0B,EAAsB;EACpF,MAAMuE,KAAK,GAAG/B,gBAAgB,CAACxC,OAAO,CAAC;EACvC,IAAIuE,KAAK,EAAE;IACT,OAAOA,KAAK;EACd;EAEA,MAAMC,cAAc,GAAG7B,qBAAqB,CAAC3C,OAAO,CAAC;EACrD,IAAIwE,cAAc,EAAE;IAClB,MAAMC,WAAW,GAAG,IAAAC,mCAAoB,EAAC1E,OAAO,CAAC;IACjD,MAAM2E,YAAY,GAAGF,WAAW,EAAEG,WAAW,CAAC;MAAEC,QAAQ,EAAEL;IAAe,CAAC,CAAC;IAC3E,IAAIG,YAAY,EAAE;MAChB,OAAO,IAAAG,2BAAc,EAACH,YAAY,CAAC;IACrC;EACF;EAEA,OAAO,IAAAG,2BAAc,EAAC9E,OAAO,CAAC;AAChC;AAIO,MAAMmD,2BAA2C,GAAAtD,OAAA,CAAAsD,2BAAA,GAAG;EACzD4B,QAAQ,EAAE,IAAI;EACdC,KAAK,EAAE,IAAI;EACXhD,MAAM,EAAE;AACV,CAAC", "ignoreList": []}