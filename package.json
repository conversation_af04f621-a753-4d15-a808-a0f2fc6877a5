{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "A mobile language learning application", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "web": "webpack serve --config webpack.config.js --mode development", "build:web": "webpack --config webpack.config.js --mode production", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx"}, "keywords": ["react-native", "language-learning", "mobile"], "author": "", "license": "ISC", "dependencies": {"react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.72.6", "react-native-web": "^0.19.9", "react-native-gesture-handler": "^2.12.0", "react-native-vector-icons": "^10.0.0", "@react-native-async-storage/async-storage": "^1.19.3", "react-native-sound": "^0.11.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/preset-react": "^7.18.6", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.0", "@react-native/metro-config": "^0.72.0", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-dom": "^18.0.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "babel-loader": "^9.1.0", "css-loader": "^6.8.1", "eslint": "^8.19.0", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.5.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "style-loader": "^3.3.3", "ts-loader": "^9.4.2", "typescript": "4.8.4", "url-loader": "^4.1.1", "webpack": "^5.88.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1", "@testing-library/react-native": "^12.3.0", "@types/jest": "^29.5.5"}}