{"version": 3, "file": "match-string-prop.js", "names": ["matchStringProp", "prop", "matcher", "match"], "sources": ["../../../src/helpers/matchers/match-string-prop.ts"], "sourcesContent": ["import { TextMatch } from '../../matches';\n\n/**\n * Matches the given string property again string or regex matcher.\n *\n * @param prop - The string prop to match.\n * @param matcher - The string or regex to match.\n * @returns - Whether the string prop matches the given string or regex.\n */\nexport function matchStringProp(prop: string | undefined, matcher: TextMatch): boolean {\n  if (!prop) {\n    return false;\n  }\n\n  if (typeof matcher === 'string') {\n    return prop === matcher;\n  }\n\n  return prop.match(matcher) != null;\n}\n"], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASA,eAAeA,CAACC,IAAwB,EAAEC,OAAkB,EAAW;EACrF,IAAI,CAACD,IAAI,EAAE;IACT,OAAO,KAAK;EACd;EAEA,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;IAC/B,OAAOD,IAAI,KAAKC,OAAO;EACzB;EAEA,OAAOD,IAAI,CAACE,KAAK,CAACD,OAAO,CAAC,IAAI,IAAI;AACpC", "ignoreList": []}