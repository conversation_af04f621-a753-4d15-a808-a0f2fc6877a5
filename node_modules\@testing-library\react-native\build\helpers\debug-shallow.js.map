{"version": 3, "file": "debug-shallow.js", "names": ["_shallow", "require", "_format", "_interopRequireDefault", "e", "__esModule", "default", "debugShallow", "instance", "message", "output", "shallowInternal", "console", "log", "format"], "sources": ["../../src/helpers/debug-shallow.ts"], "sourcesContent": ["import * as React from 'react';\nimport type { ReactTestInstance } from 'react-test-renderer';\nimport { shallowInternal } from '../shallow';\nimport format from './format';\n\n/**\n * Log pretty-printed shallow test component instance\n */\nexport default function debugShallow(\n  instance: ReactTestInstance | React.ReactElement<any>,\n  message?: string,\n) {\n  const { output } = shallowInternal(instance);\n\n  if (message) {\n    // eslint-disable-next-line no-console\n    console.log(`${message}\\n\\n`, format(output));\n  } else {\n    // eslint-disable-next-line no-console\n    console.log(format(output));\n  }\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAC,sBAAA,CAAAF,OAAA;AAA8B,SAAAE,uBAAAC,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAE9B;AACA;AACA;AACe,SAASG,YAAYA,CAClCC,QAAqD,EACrDC,OAAgB,EAChB;EACA,MAAM;IAAEC;EAAO,CAAC,GAAG,IAAAC,wBAAe,EAACH,QAAQ,CAAC;EAE5C,IAAIC,OAAO,EAAE;IACX;IACAG,OAAO,CAACC,GAAG,CAAC,GAAGJ,OAAO,MAAM,EAAE,IAAAK,eAAM,EAACJ,MAAM,CAAC,CAAC;EAC/C,CAAC,MAAM;IACL;IACAE,OAAO,CAACC,GAAG,CAAC,IAAAC,eAAM,EAACJ,MAAM,CAAC,CAAC;EAC7B;AACF", "ignoreList": []}