{"version": 3, "file": "to-be-visible.js", "names": ["_jestMatcherU<PERSON>s", "require", "_reactNative", "_accessibility", "_componentTree", "_hostComponentNames", "_utils", "toBeVisible", "element", "isNot", "checkHostElement", "pass", "isElementVisible", "message", "is", "matcherHint", "formatElement", "join", "accessibilityCache", "cache", "WeakMap", "isHiddenFromAccessibility", "isHiddenForStyles", "isHostModal", "props", "visible", "hostParent", "getHostParent", "flatStyle", "StyleSheet", "flatten", "style", "display", "opacity"], "sources": ["../../src/matchers/to-be-visible.tsx"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\nimport { matcherHint } from 'jest-matcher-utils';\nimport { StyleSheet } from 'react-native';\nimport { isHiddenFromAccessibility } from '../helpers/accessibility';\nimport { getHostParent } from '../helpers/component-tree';\nimport { isHostModal } from '../helpers/host-component-names';\nimport { checkHostElement, formatElement } from './utils';\n\nexport function toBeVisible(this: jest.MatcherContext, element: ReactTestInstance) {\n  if (element !== null || !this.isNot) {\n    checkHostElement(element, toBeVisible, this);\n  }\n\n  return {\n    pass: isElementVisible(element),\n    message: () => {\n      const is = this.isNot ? 'is' : 'is not';\n      return [\n        matcherHint(`${this.isNot ? '.not' : ''}.toBeVisible`, 'element', ''),\n        '',\n        `Received element ${is} visible:`,\n        formatElement(element),\n      ].join('\\n');\n    },\n  };\n}\n\nfunction isElementVisible(\n  element: ReactTestInstance,\n  accessibilityCache?: WeakMap<ReactTestInstance, boolean>,\n): boolean {\n  // Use cache to speed up repeated searches by `isHiddenFromAccessibility`.\n  const cache = accessibilityCache ?? new WeakMap<ReactTestInstance, boolean>();\n  if (isHiddenFromAccessibility(element, { cache })) {\n    return false;\n  }\n\n  if (isHiddenForStyles(element)) {\n    return false;\n  }\n\n  // Note: this seems to be a bug in React Native.\n  // PR with fix: https://github.com/facebook/react-native/pull/39157\n  if (isHostModal(element) && element.props.visible === false) {\n    return false;\n  }\n\n  const hostParent = getHostParent(element);\n  if (hostParent === null) {\n    return true;\n  }\n\n  return isElementVisible(hostParent, cache);\n}\n\nfunction isHiddenForStyles(element: ReactTestInstance) {\n  const flatStyle = StyleSheet.flatten(element.props.style);\n  return flatStyle?.display === 'none' || flatStyle?.opacity === 0;\n}\n"], "mappings": ";;;;;;AACA,IAAAA,iBAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAF,OAAA;AACA,IAAAG,cAAA,GAAAH,OAAA;AACA,IAAAI,mBAAA,GAAAJ,OAAA;AACA,IAAAK,MAAA,GAAAL,OAAA;AAEO,SAASM,WAAWA,CAA4BC,OAA0B,EAAE;EACjF,IAAIA,OAAO,KAAK,IAAI,IAAI,CAAC,IAAI,CAACC,KAAK,EAAE;IACnC,IAAAC,uBAAgB,EAACF,OAAO,EAAED,WAAW,EAAE,IAAI,CAAC;EAC9C;EAEA,OAAO;IACLI,IAAI,EAAEC,gBAAgB,CAACJ,OAAO,CAAC;IAC/BK,OAAO,EAAEA,CAAA,KAAM;MACb,MAAMC,EAAE,GAAG,IAAI,CAACL,KAAK,GAAG,IAAI,GAAG,QAAQ;MACvC,OAAO,CACL,IAAAM,6BAAW,EAAC,GAAG,IAAI,CAACN,KAAK,GAAG,MAAM,GAAG,EAAE,cAAc,EAAE,SAAS,EAAE,EAAE,CAAC,EACrE,EAAE,EACF,oBAAoBK,EAAE,WAAW,EACjC,IAAAE,oBAAa,EAACR,OAAO,CAAC,CACvB,CAACS,IAAI,CAAC,IAAI,CAAC;IACd;EACF,CAAC;AACH;AAEA,SAASL,gBAAgBA,CACvBJ,OAA0B,EAC1BU,kBAAwD,EAC/C;EACT;EACA,MAAMC,KAAK,GAAGD,kBAAkB,IAAI,IAAIE,OAAO,CAA6B,CAAC;EAC7E,IAAI,IAAAC,wCAAyB,EAACb,OAAO,EAAE;IAAEW;EAAM,CAAC,CAAC,EAAE;IACjD,OAAO,KAAK;EACd;EAEA,IAAIG,iBAAiB,CAACd,OAAO,CAAC,EAAE;IAC9B,OAAO,KAAK;EACd;;EAEA;EACA;EACA,IAAI,IAAAe,+BAAW,EAACf,OAAO,CAAC,IAAIA,OAAO,CAACgB,KAAK,CAACC,OAAO,KAAK,KAAK,EAAE;IAC3D,OAAO,KAAK;EACd;EAEA,MAAMC,UAAU,GAAG,IAAAC,4BAAa,EAACnB,OAAO,CAAC;EACzC,IAAIkB,UAAU,KAAK,IAAI,EAAE;IACvB,OAAO,IAAI;EACb;EAEA,OAAOd,gBAAgB,CAACc,UAAU,EAAEP,KAAK,CAAC;AAC5C;AAEA,SAASG,iBAAiBA,CAACd,OAA0B,EAAE;EACrD,MAAMoB,SAAS,GAAGC,uBAAU,CAACC,OAAO,CAACtB,OAAO,CAACgB,KAAK,CAACO,KAAK,CAAC;EACzD,OAAOH,SAAS,EAAEI,OAAO,KAAK,MAAM,IAAIJ,SAAS,EAAEK,OAAO,KAAK,CAAC;AAClE", "ignoreList": []}