{"version": 3, "file": "match-label-text.js", "names": ["_matches", "require", "_accessibility", "_findAll", "_matchTextContent", "matchLabelText", "root", "element", "expectedText", "options", "matchAccessibilityLabel", "matchAccessibilityLabelledBy", "computeAriaLabelledBy", "<PERSON><PERSON><PERSON><PERSON>", "matches", "computeAriaLabel", "normalizer", "exact", "nativeId", "text", "findAll", "node", "props", "nativeID", "matchTextContent", "length"], "sources": ["../../../src/helpers/matchers/match-label-text.ts"], "sourcesContent": ["import { ReactTestInstance } from 'react-test-renderer';\nimport { matches, TextMatch, TextMatchOptions } from '../../matches';\nimport { computeAriaLabel, computeAriaLabelledBy } from '../accessibility';\nimport { findAll } from '../find-all';\nimport { matchTextContent } from './match-text-content';\n\nexport function matchLabelText(\n  root: ReactTestInstance,\n  element: ReactTestInstance,\n  expectedText: TextMatch,\n  options: TextMatchOptions = {},\n) {\n  return (\n    matchAccessibilityLabel(element, expectedText, options) ||\n    matchAccessibilityLabelledBy(root, computeAriaLabelledBy(element), expectedText, options)\n  );\n}\n\nfunction matchAccessibilityLabel(\n  element: ReactTestInstance,\n  expectedLabel: TextMatch,\n  options: TextMatchOptions,\n) {\n  return matches(expectedLabel, computeAriaLabel(element), options.normalizer, options.exact);\n}\n\nfunction matchAccessibilityLabelledBy(\n  root: ReactTestInstance,\n  nativeId: string | undefined,\n  text: TextMatch,\n  options: TextMatchOptions,\n) {\n  if (!nativeId) {\n    return false;\n  }\n\n  return (\n    findAll(\n      root,\n      (node) => node.props.nativeID === nativeId && matchTextContent(node, text, options),\n    ).length > 0\n  );\n}\n"], "mappings": ";;;;;;AACA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,iBAAA,GAAAH,OAAA;AAEO,SAASI,cAAcA,CAC5BC,IAAuB,EACvBC,OAA0B,EAC1BC,YAAuB,EACvBC,OAAyB,GAAG,CAAC,CAAC,EAC9B;EACA,OACEC,uBAAuB,CAACH,OAAO,EAAEC,YAAY,EAAEC,OAAO,CAAC,IACvDE,4BAA4B,CAACL,IAAI,EAAE,IAAAM,oCAAqB,EAACL,OAAO,CAAC,EAAEC,YAAY,EAAEC,OAAO,CAAC;AAE7F;AAEA,SAASC,uBAAuBA,CAC9BH,OAA0B,EAC1BM,aAAwB,EACxBJ,OAAyB,EACzB;EACA,OAAO,IAAAK,gBAAO,EAACD,aAAa,EAAE,IAAAE,+BAAgB,EAACR,OAAO,CAAC,EAAEE,OAAO,CAACO,UAAU,EAAEP,OAAO,CAACQ,KAAK,CAAC;AAC7F;AAEA,SAASN,4BAA4BA,CACnCL,IAAuB,EACvBY,QAA4B,EAC5BC,IAAe,EACfV,OAAyB,EACzB;EACA,IAAI,CAACS,QAAQ,EAAE;IACb,OAAO,KAAK;EACd;EAEA,OACE,IAAAE,gBAAO,EACLd,IAAI,EACHe,IAAI,IAAKA,IAAI,CAACC,KAAK,CAACC,QAAQ,KAAKL,QAAQ,IAAI,IAAAM,kCAAgB,EAACH,IAAI,EAAEF,IAAI,EAAEV,OAAO,CACpF,CAAC,CAACgB,MAAM,GAAG,CAAC;AAEhB", "ignoreList": []}