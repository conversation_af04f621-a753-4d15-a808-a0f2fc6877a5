{"version": 3, "file": "to-be-selected.js", "names": ["_jestMatcherU<PERSON>s", "require", "_accessibility", "_utils", "toBeSelected", "element", "checkHostElement", "pass", "computeAriaSelected", "message", "is", "isNot", "matcherHint", "formatElement", "join"], "sources": ["../../src/matchers/to-be-selected.ts"], "sourcesContent": ["import { ReactTestInstance } from 'react-test-renderer';\nimport { matcherHint } from 'jest-matcher-utils';\nimport { computeAriaSelected } from '../helpers/accessibility';\nimport { checkHostElement, formatElement } from './utils';\n\nexport function toBeSelected(this: jest.MatcherContext, element: ReactTestInstance) {\n  checkHostElement(element, toBeSelected, this);\n\n  return {\n    pass: computeAriaSelected(element),\n    message: () => {\n      const is = this.isNot ? 'is' : 'is not';\n      return [\n        matcherHint(`${this.isNot ? '.not' : ''}.toBeSelected`, 'element', ''),\n        '',\n        `Received element ${is} selected`,\n        formatElement(element),\n      ].join('\\n');\n    },\n  };\n}\n"], "mappings": ";;;;;;AACA,IAAAA,iBAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AAEO,SAASG,YAAYA,CAA4BC,OAA0B,EAAE;EAClF,IAAAC,uBAAgB,EAACD,OAAO,EAAED,YAAY,EAAE,IAAI,CAAC;EAE7C,OAAO;IACLG,IAAI,EAAE,IAAAC,kCAAmB,EAACH,OAAO,CAAC;IAClCI,OAAO,EAAEA,CAAA,KAAM;MACb,MAAMC,EAAE,GAAG,IAAI,CAACC,KAAK,GAAG,IAAI,GAAG,QAAQ;MACvC,OAAO,CACL,IAAAC,6BAAW,EAAC,GAAG,IAAI,CAACD,KAAK,GAAG,MAAM,GAAG,EAAE,eAAe,EAAE,SAAS,EAAE,EAAE,CAAC,EACtE,EAAE,EACF,oBAAoBD,EAAE,WAAW,EACjC,IAAAG,oBAAa,EAACR,OAAO,CAAC,CACvB,CAACS,IAAI,CAAC,IAAI,CAAC;IACd;EACF,CAAC;AACH", "ignoreList": []}