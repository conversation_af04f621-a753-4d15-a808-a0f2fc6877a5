{"mappings": ";AAYA,+BAA+B;IAC7B,CAAC,IAAI,EAAE,MAAM,GAAG;QACd,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;KACtB,CAAA;CACF,CAAC;AAEF;;;GAGG;AACH;gBAIc,QAAQ,EAAE,gBAAgB,EAAE,aAAa,GAAE,MAAgB;IASvE,kBAAkB,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,MAAM;CAcxD;ACjCD;;GAEG;AACH;gBAKc,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,iBAAiB;IAMvD,MAAM,CAAC,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC,MAAM,EAAE,aAAa,GAAG,CAAC,GAAG,mBAAmB,CAAC,EAAE,MAAM,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,SAAS,GAAG,MAAM,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;CAqB1K", "sources": ["packages/@internationalized/message/src/packages/@internationalized/message/src/MessageDictionary.ts", "packages/@internationalized/message/src/packages/@internationalized/message/src/MessageFormatter.ts", "packages/@internationalized/message/src/packages/@internationalized/message/src/index.ts", "packages/@internationalized/message/src/index.ts"], "sourcesContent": [null, null, null, "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nexport type {LocalizedStrings} from './MessageDictionary';\nexport {MessageDictionary} from './MessageDictionary';\nexport {MessageFormatter} from './MessageFormatter';\n"], "names": [], "version": 3, "file": "types.d.ts.map"}