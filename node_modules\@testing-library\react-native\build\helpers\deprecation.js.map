{"version": 3, "file": "deprecation.js", "names": ["_queryName", "require", "deprecateQueries", "queriesObject", "recommendation", "result", "Object", "keys", "for<PERSON>ach", "queryName", "queryFn", "deprecate<PERSON><PERSON><PERSON>", "formattedRecommendation", "replace", "getQueryPrefix", "wrapper", "args", "errorMessage", "console", "warn", "warned", "printDeprecationWarning", "functionName"], "sources": ["../../src/helpers/deprecation.ts"], "sourcesContent": ["import { getQueryPrefix } from './query-name';\n\nexport function deprecateQueries<Queries extends Record<string, any>>(\n  queriesObject: Queries,\n  recommendation: string,\n): Queries {\n  const result = {} as Queries;\n  Object.keys(queriesObject).forEach((queryName) => {\n    const queryFn = queriesObject[queryName];\n    // @ts-expect-error: generic typing is hard\n    result[queryName] = deprecateQuery(queryFn, queryName, recommendation);\n  });\n\n  return result;\n}\n\nfunction deprecateQuery<QueryFn extends (...args: any) => any>(\n  queryFn: QueryFn,\n  queryName: string,\n  recommendation: string,\n): QueryFn {\n  const formattedRecommendation = recommendation.replace(\n    /{queryPrefix}/g,\n    getQueryPrefix(queryName),\n  );\n\n  // @ts-expect-error: generic typing is hard\n  const wrapper: QueryFn = (...args: any) => {\n    const errorMessage = `${queryName}(...) is deprecated and will be removed in the future.\\n\\n${formattedRecommendation}`;\n    // eslint-disable-next-line no-console\n    console.warn(errorMessage);\n    return queryFn(...args);\n  };\n\n  return wrapper;\n}\n\nconst warned: { [functionName: string]: boolean } = {};\n\n/* istanbul ignore next: occasionally used */\nexport function printDeprecationWarning(functionName: string) {\n  if (warned[functionName]) {\n    return;\n  }\n\n  // eslint-disable-next-line no-console\n  console.warn(`\n  Deprecation Warning:\n  Use of ${functionName} is not recommended and will be deleted in future versions of @testing-library/react-native.\n  `);\n\n  warned[functionName] = true;\n}\n"], "mappings": ";;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAEO,SAASC,gBAAgBA,CAC9BC,aAAsB,EACtBC,cAAsB,EACb;EACT,MAAMC,MAAM,GAAG,CAAC,CAAY;EAC5BC,MAAM,CAACC,IAAI,CAACJ,aAAa,CAAC,CAACK,OAAO,CAAEC,SAAS,IAAK;IAChD,MAAMC,OAAO,GAAGP,aAAa,CAACM,SAAS,CAAC;IACxC;IACAJ,MAAM,CAACI,SAAS,CAAC,GAAGE,cAAc,CAACD,OAAO,EAAED,SAAS,EAAEL,cAAc,CAAC;EACxE,CAAC,CAAC;EAEF,OAAOC,MAAM;AACf;AAEA,SAASM,cAAcA,CACrBD,OAAgB,EAChBD,SAAiB,EACjBL,cAAsB,EACb;EACT,MAAMQ,uBAAuB,GAAGR,cAAc,CAACS,OAAO,CACpD,gBAAgB,EAChB,IAAAC,yBAAc,EAACL,SAAS,CAC1B,CAAC;;EAED;EACA,MAAMM,OAAgB,GAAGA,CAAC,GAAGC,IAAS,KAAK;IACzC,MAAMC,YAAY,GAAG,GAAGR,SAAS,6DAA6DG,uBAAuB,EAAE;IACvH;IACAM,OAAO,CAACC,IAAI,CAACF,YAAY,CAAC;IAC1B,OAAOP,OAAO,CAAC,GAAGM,IAAI,CAAC;EACzB,CAAC;EAED,OAAOD,OAAO;AAChB;AAEA,MAAMK,MAA2C,GAAG,CAAC,CAAC;;AAEtD;AACO,SAASC,uBAAuBA,CAACC,YAAoB,EAAE;EAC5D,IAAIF,MAAM,CAACE,YAAY,CAAC,EAAE;IACxB;EACF;;EAEA;EACAJ,OAAO,CAACC,IAAI,CAAC;AACf;AACA,WAAWG,YAAY;AACvB,GAAG,CAAC;EAEFF,MAAM,CAACE,YAAY,CAAC,GAAG,IAAI;AAC7B", "ignoreList": []}