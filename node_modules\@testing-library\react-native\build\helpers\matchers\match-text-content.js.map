{"version": 3, "file": "match-text-content.js", "names": ["_matches", "require", "_textContent", "matchTextContent", "node", "text", "options", "textContent", "getTextContent", "exact", "normalizer", "matches"], "sources": ["../../../src/helpers/matchers/match-text-content.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\nimport { matches, TextMatch, TextMatchOptions } from '../../matches';\nimport { getTextContent } from '../text-content';\n\n/**\n * Matches the given node's text content against string or regex matcher.\n *\n * @param node - Node which text content will be matched\n * @param text - The string or regex to match.\n * @returns - Whether the node's text content matches the given string or regex.\n */\nexport function matchTextContent(\n  node: ReactTestInstance,\n  text: TextMatch,\n  options: TextMatchOptions = {},\n) {\n  const textContent = getTextContent(node);\n  const { exact, normalizer } = options;\n  return matches(text, textContent, normalizer, exact);\n}\n"], "mappings": ";;;;;;AACA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASE,gBAAgBA,CAC9BC,IAAuB,EACvBC,IAAe,EACfC,OAAyB,GAAG,CAAC,CAAC,EAC9B;EACA,MAAMC,WAAW,GAAG,IAAAC,2BAAc,EAACJ,IAAI,CAAC;EACxC,MAAM;IAAEK,KAAK;IAAEC;EAAW,CAAC,GAAGJ,OAAO;EACrC,OAAO,IAAAK,gBAAO,EAACN,IAAI,EAAEE,WAAW,EAAEG,UAAU,EAAED,KAAK,CAAC;AACtD", "ignoreList": []}