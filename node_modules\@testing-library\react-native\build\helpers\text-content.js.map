{"version": 3, "file": "text-content.js", "names": ["getTextContent", "element", "result", "children", "for<PERSON>ach", "child", "push", "join"], "sources": ["../../src/helpers/text-content.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\n\nexport function getTextContent(element: ReactTestInstance | string | null): string {\n  if (!element) {\n    return '';\n  }\n\n  if (typeof element === 'string') {\n    return element;\n  }\n\n  const result: string[] = [];\n  element.children?.forEach((child) => {\n    result.push(getTextContent(child));\n  });\n\n  return result.join('');\n}\n"], "mappings": ";;;;;;AAEO,SAASA,cAAcA,CAACC,OAA0C,EAAU;EACjF,IAAI,CAACA,OAAO,EAAE;IACZ,OAAO,EAAE;EACX;EAEA,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;IAC/B,OAAOA,OAAO;EAChB;EAEA,MAAMC,MAAgB,GAAG,EAAE;EAC3BD,OAAO,CAACE,QAAQ,EAAEC,OAAO,CAAEC,KAAK,IAAK;IACnCH,MAAM,CAACI,IAAI,CAACN,cAAc,CAACK,KAAK,CAAC,CAAC;EACpC,CAAC,CAAC;EAEF,OAAOH,MAAM,CAACK,IAAI,CAAC,EAAE,CAAC;AACxB", "ignoreList": []}