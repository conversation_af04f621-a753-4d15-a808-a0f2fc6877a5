/* Web-specific styles for LingoLearn */

/* Global styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

/* App container */
#app-root {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
}

/* Card hover effects */
.word-card {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  cursor: pointer;
}

.word-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Button hover effects */
.action-button {
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

.action-button:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.pronunciation-button {
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

.pronunciation-button:hover {
  transform: scale(1.05);
  background-color: #2563eb;
}

/* Responsive design */
@media (max-width: 768px) {
  #app-root {
    padding: 10px;
  }
  
  .word-card {
    width: 90vw;
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  .word-card {
    width: 95vw;
    height: 180px;
  }
}

/* Loading animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

/* Keyboard navigation hints */
.keyboard-hints {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px 20px;
  border-radius: 20px;
  font-size: 12px;
  opacity: 0.7;
  pointer-events: none;
}

@media (max-width: 768px) {
  .keyboard-hints {
    display: none;
  }
}

/* Focus styles for accessibility */
.focusable:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .action-button,
  .pronunciation-button,
  .keyboard-hints {
    display: none;
  }
}
