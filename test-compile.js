// Simple compilation test
const fs = require('fs');
const path = require('path');

console.log('Testing project compilation...');

// Check if all required files exist
const requiredFiles = [
  'App.tsx',
  'index.js',
  'src/screens/WordLearningScreen.tsx',
  'src/components/WordLearning/WordCard.tsx',
  'src/components/WordLearning/PronunciationButton.tsx',
  'src/components/WordLearning/ActionButtons.tsx',
  'src/services/wordService.ts',
  'src/types/wordLearning.ts'
];

let allFilesExist = true;

requiredFiles.forEach(file => {
  if (fs.existsSync(path.join(__dirname, file))) {
    console.log(`✓ ${file} exists`);
  } else {
    console.log(`✗ ${file} missing`);
    allFilesExist = false;
  }
});

if (allFilesExist) {
  console.log('\n✓ All required files are present!');
  console.log('Project structure looks good.');
} else {
  console.log('\n✗ Some files are missing.');
  process.exit(1);
}
