{"version": 3, "file": "to-be-disabled.js", "names": ["_jestMatcherU<PERSON>s", "require", "_accessibility", "_componentTree", "_utils", "toBeDisabled", "element", "checkHostElement", "isDisabled", "computeAriaDisabled", "isAncestorDisabled", "pass", "message", "is", "isNot", "matcherHint", "formatElement", "join", "toBeEnabled", "isEnabled", "parent", "getHostParent"], "sources": ["../../src/matchers/to-be-disabled.tsx"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\nimport { matcherHint } from 'jest-matcher-utils';\nimport { computeAriaDisabled } from '../helpers/accessibility';\nimport { getHostParent } from '../helpers/component-tree';\nimport { checkHostElement, formatElement } from './utils';\n\nexport function toBeDisabled(this: jest.MatcherContext, element: ReactTestInstance) {\n  checkHostElement(element, toBeDisabled, this);\n\n  const isDisabled = computeAriaDisabled(element) || isAncestorDisabled(element);\n\n  return {\n    pass: isDisabled,\n    message: () => {\n      const is = this.isNot ? 'is' : 'is not';\n      return [\n        matcherHint(`${this.isNot ? '.not' : ''}.toBeDisabled`, 'element', ''),\n        '',\n        `Received element ${is} disabled:`,\n        formatElement(element),\n      ].join('\\n');\n    },\n  };\n}\n\nexport function toBeEnabled(this: jest.MatcherContext, element: ReactTestInstance) {\n  checkHostElement(element, toBeEnabled, this);\n\n  const isEnabled = !computeAriaDisabled(element) && !isAncestorDisabled(element);\n\n  return {\n    pass: isEnabled,\n    message: () => {\n      const is = this.isNot ? 'is' : 'is not';\n      return [\n        matcherHint(`${this.isNot ? '.not' : ''}.toBeEnabled`, 'element', ''),\n        '',\n        `Received element ${is} enabled:`,\n        formatElement(element),\n      ].join('\\n');\n    },\n  };\n}\n\nfunction isAncestorDisabled(element: ReactTestInstance): boolean {\n  const parent = getHostParent(element);\n  if (parent == null) {\n    return false;\n  }\n\n  return computeAriaDisabled(parent) || isAncestorDisabled(parent);\n}\n"], "mappings": ";;;;;;;AACA,IAAAA,iBAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AAEO,SAASI,YAAYA,CAA4BC,OAA0B,EAAE;EAClF,IAAAC,uBAAgB,EAACD,OAAO,EAAED,YAAY,EAAE,IAAI,CAAC;EAE7C,MAAMG,UAAU,GAAG,IAAAC,kCAAmB,EAACH,OAAO,CAAC,IAAII,kBAAkB,CAACJ,OAAO,CAAC;EAE9E,OAAO;IACLK,IAAI,EAAEH,UAAU;IAChBI,OAAO,EAAEA,CAAA,KAAM;MACb,MAAMC,EAAE,GAAG,IAAI,CAACC,KAAK,GAAG,IAAI,GAAG,QAAQ;MACvC,OAAO,CACL,IAAAC,6BAAW,EAAC,GAAG,IAAI,CAACD,KAAK,GAAG,MAAM,GAAG,EAAE,eAAe,EAAE,SAAS,EAAE,EAAE,CAAC,EACtE,EAAE,EACF,oBAAoBD,EAAE,YAAY,EAClC,IAAAG,oBAAa,EAACV,OAAO,CAAC,CACvB,CAACW,IAAI,CAAC,IAAI,CAAC;IACd;EACF,CAAC;AACH;AAEO,SAASC,WAAWA,CAA4BZ,OAA0B,EAAE;EACjF,IAAAC,uBAAgB,EAACD,OAAO,EAAEY,WAAW,EAAE,IAAI,CAAC;EAE5C,MAAMC,SAAS,GAAG,CAAC,IAAAV,kCAAmB,EAACH,OAAO,CAAC,IAAI,CAACI,kBAAkB,CAACJ,OAAO,CAAC;EAE/E,OAAO;IACLK,IAAI,EAAEQ,SAAS;IACfP,OAAO,EAAEA,CAAA,KAAM;MACb,MAAMC,EAAE,GAAG,IAAI,CAACC,KAAK,GAAG,IAAI,GAAG,QAAQ;MACvC,OAAO,CACL,IAAAC,6BAAW,EAAC,GAAG,IAAI,CAACD,KAAK,GAAG,MAAM,GAAG,EAAE,cAAc,EAAE,SAAS,EAAE,EAAE,CAAC,EACrE,EAAE,EACF,oBAAoBD,EAAE,WAAW,EACjC,IAAAG,oBAAa,EAACV,OAAO,CAAC,CACvB,CAACW,IAAI,CAAC,IAAI,CAAC;IACd;EACF,CAAC;AACH;AAEA,SAASP,kBAAkBA,CAACJ,OAA0B,EAAW;EAC/D,MAAMc,MAAM,GAAG,IAAAC,4BAAa,EAACf,OAAO,CAAC;EACrC,IAAIc,MAAM,IAAI,IAAI,EAAE;IAClB,OAAO,KAAK;EACd;EAEA,OAAO,IAAAX,kCAAmB,EAACW,MAAM,CAAC,IAAIV,kBAAkB,CAACU,MAAM,CAAC;AAClE", "ignoreList": []}