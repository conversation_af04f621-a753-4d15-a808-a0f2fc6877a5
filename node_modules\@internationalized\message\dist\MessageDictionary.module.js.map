{"mappings": "AAAA;;;;;;;;;;CAUC,GAYM,MAAM;IAaX,mBAAmB,GAAW,EAAE,MAAc,EAAU;QACtD,IAAI,UAAU,IAAI,CAAC,QAAQ,CAAC,OAAO;QACnC,IAAI,CAAC,SAAS;YACZ,UAAU,0CAAoB,QAAQ,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,aAAa;YACvE,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG;QAC1B;QAEA,IAAI,SAAS,OAAO,CAAC,IAAI;QACzB,IAAI,CAAC,QACH,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,IAAI,IAAI,EAAE,OAAO,OAAO,CAAC;QAG1E,OAAO;IACT;IAtBA,YAAY,QAA0B,EAAE,gBAAwB,OAAO,CAAE;QACvE,yDAAyD;QACzD,uGAAuG;QACvG,IAAI,CAAC,QAAQ,GAAG,OAAO,WAAW,CAChC,OAAO,OAAO,CAAC,UAAU,MAAM,CAAC,CAAC,GAAG,EAAE,GAAK;QAE7C,IAAI,CAAC,aAAa,GAAG;IACvB;AAgBF;AAEA,SAAS,0CAAoB,MAAc,EAAE,OAAyB,EAAE,gBAAgB,OAAO;IAC7F,sCAAsC;IACtC,IAAI,OAAO,CAAC,OAAO,EACjB,OAAO,OAAO,CAAC,OAAO;IAGxB,iDAAiD;IACjD,2EAA2E;IAC3E,8CAA8C;IAC9C,uEAAuE;IACvE,sDAAsD;IACtD,IAAI,WAAW,kCAAY;IAC3B,IAAI,OAAO,CAAC,SAAS,EACnB,OAAO,OAAO,CAAC,SAAS;IAG1B,IAAK,IAAI,OAAO,QAAS;QACvB,IAAI,IAAI,UAAU,CAAC,WAAW,MAC5B,OAAO,OAAO,CAAC,IAAI;IAEvB;IAEA,8BAA8B;IAC9B,OAAO,OAAO,CAAC,cAAc;AAC/B;AAEA,SAAS,kCAAY,MAAc;IACjC,IAAI,KAAK,MAAM,EACb,OAAO,IAAI,KAAK,MAAM,CAAC,QAAQ,QAAQ;IAGzC,OAAO,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE;AAC7B", "sources": ["packages/@internationalized/message/src/MessageDictionary.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nexport type LocalizedStrings = {\n  [lang: string]: {\n    [key: string]: string\n  }\n};\n\n/**\n * Stores a mapping of localized strings. Can be used to find the\n * closest available string for a given locale.\n */\nexport class MessageDictionary {\n  private messages: LocalizedStrings;\n  private defaultLocale: string;\n\n  constructor(messages: LocalizedStrings, defaultLocale: string = 'en-US') {\n    // Clone messages so we don't modify the original object.\n    // Filter out entries with falsy values which may have been caused by applying optimize-locales-plugin.\n    this.messages = Object.fromEntries(\n      Object.entries(messages).filter(([, v]) => v)\n    );\n    this.defaultLocale = defaultLocale;\n  }\n\n  getStringForLocale(key: string, locale: string): string {\n    let strings = this.messages[locale];\n    if (!strings) {\n      strings = getStringsForLocale(locale, this.messages, this.defaultLocale);\n      this.messages[locale] = strings;\n    }\n\n    let string = strings[key];\n    if (!string) {\n      throw new Error(`Could not find intl message ${key} in ${locale} locale`);\n    }\n\n    return string;\n  }\n}\n\nfunction getStringsForLocale(locale: string, strings: LocalizedStrings, defaultLocale = 'en-US') {\n  // If there is an exact match, use it.\n  if (strings[locale]) {\n    return strings[locale];\n  }\n\n  // Attempt to find the closest match by language.\n  // For example, if the locale is fr-CA (French Canadian), but there is only\n  // an fr-FR (France) set of strings, use that.\n  // This could be replaced with Intl.LocaleMatcher once it is supported.\n  // https://github.com/tc39/proposal-intl-localematcher\n  let language = getLanguage(locale);\n  if (strings[language]) {\n    return strings[language];\n  }\n\n  for (let key in strings) {\n    if (key.startsWith(language + '-')) {\n      return strings[key];\n    }\n  }\n\n  // Nothing close, use english.\n  return strings[defaultLocale];\n}\n\nfunction getLanguage(locale: string) {\n  if (Intl.Locale) {\n    return new Intl.Locale(locale).language;\n  }\n\n  return locale.split('-')[0];\n}\n"], "names": [], "version": 3, "file": "MessageDictionary.module.js.map"}