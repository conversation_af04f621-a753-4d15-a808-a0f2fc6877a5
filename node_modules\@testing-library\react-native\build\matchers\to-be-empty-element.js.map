{"version": 3, "file": "to-be-empty-element.js", "names": ["_jestMatcherU<PERSON>s", "require", "_componentTree", "_utils", "toBeEmptyElement", "element", "checkHostElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getHostChildren", "pass", "length", "message", "matcherHint", "isNot", "RECEIVED_COLOR", "formatElementArray", "join"], "sources": ["../../src/matchers/to-be-empty-element.tsx"], "sourcesContent": ["import { ReactTestInstance } from 'react-test-renderer';\nimport { matcherHint, RECEIVED_COLOR } from 'jest-matcher-utils';\nimport { getHostChildren } from '../helpers/component-tree';\nimport { checkHostElement, formatElementArray } from './utils';\n\nexport function toBeEmptyElement(this: jest.MatcherContext, element: ReactTestInstance) {\n  checkHostElement(element, toBeEmptyElement, this);\n\n  const hostChildren = getHostChildren(element);\n\n  return {\n    pass: hostChildren.length === 0,\n    message: () => {\n      return [\n        matcherHint(`${this.isNot ? '.not' : ''}.toBeEmptyElement`, 'element', ''),\n        '',\n        'Received:',\n        `${RECEIVED_COLOR(formatElementArray(hostChildren))}`,\n      ].join('\\n');\n    },\n  };\n}\n"], "mappings": ";;;;;;AACA,IAAAA,iBAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AAEO,SAASG,gBAAgBA,CAA4BC,OAA0B,EAAE;EACtF,IAAAC,uBAAgB,EAACD,OAAO,EAAED,gBAAgB,EAAE,IAAI,CAAC;EAEjD,MAAMG,YAAY,GAAG,IAAAC,8BAAe,EAACH,OAAO,CAAC;EAE7C,OAAO;IACLI,IAAI,EAAEF,YAAY,CAACG,MAAM,KAAK,CAAC;IAC/BC,OAAO,EAAEA,CAAA,KAAM;MACb,OAAO,CACL,IAAAC,6BAAW,EAAC,GAAG,IAAI,CAACC,KAAK,GAAG,MAAM,GAAG,EAAE,mBAAmB,EAAE,SAAS,EAAE,EAAE,CAAC,EAC1E,EAAE,EACF,WAAW,EACX,GAAG,IAAAC,gCAAc,EAAC,IAAAC,yBAAkB,EAACR,YAAY,CAAC,CAAC,EAAE,CACtD,CAACS,IAAI,CAAC,IAAI,CAAC;IACd;EACF,CAAC;AACH", "ignoreList": []}