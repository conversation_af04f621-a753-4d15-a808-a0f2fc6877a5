{"version": 3, "file": "object.js", "names": ["pick", "object", "keys", "result", "for<PERSON>ach", "key", "undefined", "isObject", "value", "Array", "isArray", "removeUndefinedKeys", "prop", "<PERSON><PERSON><PERSON><PERSON>", "Object", "entries"], "sources": ["../../src/helpers/object.ts"], "sourcesContent": ["export function pick<T extends {}>(object: T, keys: (keyof T)[]): Partial<T> {\n  const result: Partial<T> = {};\n  keys.forEach((key) => {\n    if (object[key] !== undefined) {\n      result[key] = object[key];\n    }\n  });\n\n  return result;\n}\n\nfunction isObject(value: unknown): value is Record<string, unknown> {\n  return value !== null && typeof value === 'object' && !Array.isArray(value);\n}\n\nexport function removeUndefinedKeys(prop: unknown) {\n  if (!isObject(prop)) {\n    return prop;\n  }\n\n  let hasKeys = false;\n  const result: Record<string, unknown> = {};\n  Object.entries(prop).forEach(([key, value]) => {\n    if (value !== undefined) {\n      result[key] = value;\n      hasKeys = true;\n    }\n  });\n\n  return hasKeys ? result : undefined;\n}\n"], "mappings": ";;;;;;;AAAO,SAASA,IAAIA,CAAeC,MAAS,EAAEC,IAAiB,EAAc;EAC3E,MAAMC,MAAkB,GAAG,CAAC,CAAC;EAC7BD,IAAI,CAACE,OAAO,CAAEC,GAAG,IAAK;IACpB,IAAIJ,MAAM,CAACI,GAAG,CAAC,KAAKC,SAAS,EAAE;MAC7BH,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;IAC3B;EACF,CAAC,CAAC;EAEF,OAAOF,MAAM;AACf;AAEA,SAASI,QAAQA,CAACC,KAAc,EAAoC;EAClE,OAAOA,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC;AAC7E;AAEO,SAASG,mBAAmBA,CAACC,IAAa,EAAE;EACjD,IAAI,CAACL,QAAQ,CAACK,IAAI,CAAC,EAAE;IACnB,OAAOA,IAAI;EACb;EAEA,IAAIC,OAAO,GAAG,KAAK;EACnB,MAAMV,MAA+B,GAAG,CAAC,CAAC;EAC1CW,MAAM,CAACC,OAAO,CAACH,IAAI,CAAC,CAACR,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEG,KAAK,CAAC,KAAK;IAC7C,IAAIA,KAAK,KAAKF,SAAS,EAAE;MACvBH,MAAM,CAACE,GAAG,CAAC,GAAGG,KAAK;MACnBK,OAAO,GAAG,IAAI;IAChB;EACF,CAAC,CAAC;EAEF,OAAOA,OAAO,GAAGV,MAAM,GAAGG,SAAS;AACrC", "ignoreList": []}