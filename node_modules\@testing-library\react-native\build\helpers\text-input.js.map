{"version": 3, "file": "text-input.js", "names": ["_nativeState", "require", "_hostComponentNames", "isTextInputEditable", "element", "isHostTextInput", "Error", "type", "props", "editable", "getTextInputValue", "value", "nativeState", "valueForElement", "get", "defaultValue"], "sources": ["../../src/helpers/text-input.ts"], "sourcesContent": ["import { ReactTestInstance } from 'react-test-renderer';\nimport { nativeState } from '../native-state';\nimport { isHostTextInput } from './host-component-names';\n\nexport function isTextInputEditable(element: ReactTestInstance) {\n  if (!isHostTextInput(element)) {\n    throw new Error(`Element is not a \"TextInput\", but it has type \"${element.type}\".`);\n  }\n\n  return element.props.editable !== false;\n}\n\nexport function getTextInputValue(element: ReactTestInstance) {\n  if (!isHostTextInput(element)) {\n    throw new Error(`Element is not a \"TextInput\", but it has type \"${element.type}\".`);\n  }\n\n  return (\n    element.props.value ??\n    nativeState.valueForElement.get(element) ??\n    element.props.defaultValue ??\n    ''\n  );\n}\n"], "mappings": ";;;;;;;AACA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,mBAAA,GAAAD,OAAA;AAEO,SAASE,mBAAmBA,CAACC,OAA0B,EAAE;EAC9D,IAAI,CAAC,IAAAC,mCAAe,EAACD,OAAO,CAAC,EAAE;IAC7B,MAAM,IAAIE,KAAK,CAAC,kDAAkDF,OAAO,CAACG,IAAI,IAAI,CAAC;EACrF;EAEA,OAAOH,OAAO,CAACI,KAAK,CAACC,QAAQ,KAAK,KAAK;AACzC;AAEO,SAASC,iBAAiBA,CAACN,OAA0B,EAAE;EAC5D,IAAI,CAAC,IAAAC,mCAAe,EAACD,OAAO,CAAC,EAAE;IAC7B,MAAM,IAAIE,KAAK,CAAC,kDAAkDF,OAAO,CAACG,IAAI,IAAI,CAAC;EACrF;EAEA,OACEH,OAAO,CAACI,KAAK,CAACG,KAAK,IACnBC,wBAAW,CAACC,eAAe,CAACC,GAAG,CAACV,OAAO,CAAC,IACxCA,OAAO,CAACI,KAAK,CAACO,YAAY,IAC1B,EAAE;AAEN", "ignoreList": []}