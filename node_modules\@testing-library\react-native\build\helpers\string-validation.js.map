{"version": 3, "file": "string-validation.js", "names": ["validateStringsRenderedWithinText", "rendererJSON", "Array", "isArray", "for<PERSON>ach", "validateStringsRenderedWithinTextForNode", "exports", "node", "type", "children", "child", "Error"], "sources": ["../../src/helpers/string-validation.ts"], "sourcesContent": ["import { ReactTestRendererNode } from 'react-test-renderer';\n\nexport const validateStringsRenderedWithinText = (\n  rendererJSON: ReactTestRendererNode | Array<ReactTestRendererNode> | null,\n) => {\n  if (!rendererJSON) return;\n\n  if (Array.isArray(rendererJSON)) {\n    rendererJSON.forEach(validateStringsRenderedWithinTextForNode);\n    return;\n  }\n\n  return validateStringsRenderedWithinTextForNode(rendererJSON);\n};\n\nconst validateStringsRenderedWithinTextForNode = (node: ReactTestRendererNode) => {\n  if (typeof node === 'string') {\n    return;\n  }\n\n  if (node.type !== 'Text') {\n    node.children?.forEach((child) => {\n      if (typeof child === 'string') {\n        throw new Error(\n          `Invariant Violation: Text strings must be rendered within a <Text> component. Detected attempt to render \"${child}\" string within a <${node.type}> component.`,\n        );\n      }\n    });\n  }\n\n  if (node.children) {\n    node.children.forEach(validateStringsRenderedWithinTextForNode);\n  }\n};\n"], "mappings": ";;;;;;AAEO,MAAMA,iCAAiC,GAC5CC,YAAyE,IACtE;EACH,IAAI,CAACA,YAAY,EAAE;EAEnB,IAAIC,KAAK,CAACC,OAAO,CAACF,YAAY,CAAC,EAAE;IAC/BA,YAAY,CAACG,OAAO,CAACC,wCAAwC,CAAC;IAC9D;EACF;EAEA,OAAOA,wCAAwC,CAACJ,YAAY,CAAC;AAC/D,CAAC;AAACK,OAAA,CAAAN,iCAAA,GAAAA,iCAAA;AAEF,MAAMK,wCAAwC,GAAIE,IAA2B,IAAK;EAChF,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC5B;EACF;EAEA,IAAIA,IAAI,CAACC,IAAI,KAAK,MAAM,EAAE;IACxBD,IAAI,CAACE,QAAQ,EAAEL,OAAO,CAAEM,KAAK,IAAK;MAChC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,MAAM,IAAIC,KAAK,CACb,6GAA6GD,KAAK,sBAAsBH,IAAI,CAACC,IAAI,cACnJ,CAAC;MACH;IACF,CAAC,CAAC;EACJ;EAEA,IAAID,IAAI,CAACE,QAAQ,EAAE;IACjBF,IAAI,CAACE,QAAQ,CAACL,OAAO,CAACC,wCAAwC,CAAC;EACjE;AACF,CAAC", "ignoreList": []}