{"mappings": ";;;;;;;;;;;;AAAA;;;;;;;;;;CAUC;AASM,MAAM;IAWX,OAAiB,GAAW,EAAE,SAA6G,EAA+B;QACxK,IAAI,UAAU,IAAI,CAAC,KAAK,CAAC,IAAI;QAC7B,IAAI,CAAC,SAAS;YACZ,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,KAAK,IAAI,CAAC,MAAM;YAC3D,IAAI,CAAC,KACH,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,IAAI,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;YAG/E,UAAU,IAAI,CAAA,GAAA,kDAAgB,EAAE,KAAK,IAAI,CAAC,MAAM;YAChD,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG;QACpB;QACA,IAAI;QACJ,IAAI,WACF,UAAU,OAAO,IAAI,CAAC,WAAW,MAAM,CAAC,CAAC,KAAK;YAC5C,GAAG,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,IAAI,OAAO,QAAQ,SAAS,CAAC,IAAI;YAC1D,OAAO;QACT,GAAG,CAAC;QAGN,OAAO,QAAQ,MAAM,CAAC;IACxB;IA1BA,YAAY,MAAc,EAAE,QAA2B,CAAE;QACvD,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,KAAK,GAAG,CAAC;IAChB;AAuBF", "sources": ["packages/@internationalized/message/src/MessageFormatter.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {FormatXMLElementFn, PrimitiveType} from 'intl-messageformat/src/formatters';\nimport IntlMessageFormat from 'intl-messageformat';\nimport {MessageDictionary} from './MessageDictionary';\n\n/**\n * Formats ICU Message strings to create localized strings from a MessageDictionary.\n */\nexport class MessageFormatter {\n  private locale: string;\n  private messages: MessageDictionary;\n  private cache: {[key: string]: IntlMessageFormat};\n\n  constructor(locale: string, messages: MessageDictionary) {\n    this.locale = locale;\n    this.messages = messages;\n    this.cache = {};\n  }\n\n  format<T = void>(key: string, variables: Record<string, PrimitiveType | T | FormatXMLElementFn<T, string | T | (string | T)[]>> | undefined): string | T | (string | T)[] {\n    let message = this.cache[key];\n    if (!message) {\n      let msg = this.messages.getStringForLocale(key, this.locale);\n      if (!msg) {\n        throw new Error(`Could not find intl message ${key} in ${this.locale} locale`);\n      }\n\n      message = new IntlMessageFormat(msg, this.locale);\n      this.cache[key] = message;\n    }\n    let varCopy: Record<string, PrimitiveType | T | FormatXMLElementFn<T, string | T | (string | T)[]>> | undefined;\n    if (variables) {\n      varCopy = Object.keys(variables).reduce((acc, key) => {\n        acc[key] = variables[key] == null ? false : variables[key];\n        return acc;\n      }, {});\n    }\n\n    return message.format(varCopy);\n  }\n}\n"], "names": [], "version": 3, "file": "MessageFormatter.main.js.map"}