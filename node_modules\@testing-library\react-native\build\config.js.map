{"version": 3, "file": "config.js", "names": ["defaultConfig", "asyncUtilTimeout", "defaultIncludeHiddenElements", "concurrentRoot", "config", "configure", "options", "defaultHidden", "restOptions", "configureInternal", "option", "resetToDefaults", "getConfig"], "sources": ["../src/config.ts"], "sourcesContent": ["import { DebugOptions } from './helpers/debug-deep';\n\n/**\n * Global configuration options for React Native Testing Library.\n */\n\nexport type Config = {\n  /** Default timeout, in ms, for `waitFor` and `findBy*` queries. */\n  asyncUtilTimeout: number;\n\n  /** Default value for `includeHiddenElements` query option. */\n  defaultIncludeHiddenElements: boolean;\n\n  /** Default options for `debug` helper. */\n  defaultDebugOptions?: Partial<DebugOptions>;\n\n  /**\n   * Set to `true` to enable concurrent rendering.\n   * Otherwise `render` will default to legacy synchronous rendering.\n   */\n  concurrentRoot: boolean;\n};\n\nexport type ConfigAliasOptions = {\n  /** RTL-compatibility alias to `defaultIncludeHiddenElements` */\n  defaultHidden: boolean;\n};\n\nexport type HostComponentNames = {\n  text: string;\n  textInput: string;\n  image: string;\n  switch: string;\n  scrollView: string;\n  modal: string;\n};\n\nexport type InternalConfig = Config & {\n  /** Names for key React Native host components. */\n  hostComponentNames?: HostComponentNames;\n};\n\nconst defaultConfig: InternalConfig = {\n  asyncUtilTimeout: 1000,\n  defaultIncludeHiddenElements: false,\n  concurrentRoot: false,\n};\n\nlet config = { ...defaultConfig };\n\n/**\n * Configure global options for React Native Testing Library.\n */\nexport function configure(options: Partial<Config & ConfigAliasOptions>) {\n  const { defaultHidden, ...restOptions } = options;\n\n  const defaultIncludeHiddenElements =\n    restOptions.defaultIncludeHiddenElements ??\n    defaultHidden ??\n    config.defaultIncludeHiddenElements;\n\n  config = {\n    ...config,\n    ...restOptions,\n    defaultIncludeHiddenElements,\n  };\n}\n\nexport function configureInternal(option: Partial<InternalConfig>) {\n  config = {\n    ...config,\n    ...option,\n  };\n}\n\nexport function resetToDefaults() {\n  config = { ...defaultConfig };\n}\n\nexport function getConfig() {\n  return config;\n}\n"], "mappings": ";;;;;;;;;AAEA;AACA;AACA;;AAsCA,MAAMA,aAA6B,GAAG;EACpCC,gBAAgB,EAAE,IAAI;EACtBC,4BAA4B,EAAE,KAAK;EACnCC,cAAc,EAAE;AAClB,CAAC;AAED,IAAIC,MAAM,GAAG;EAAE,GAAGJ;AAAc,CAAC;;AAEjC;AACA;AACA;AACO,SAASK,SAASA,CAACC,OAA6C,EAAE;EACvE,MAAM;IAAEC,aAAa;IAAE,GAAGC;EAAY,CAAC,GAAGF,OAAO;EAEjD,MAAMJ,4BAA4B,GAChCM,WAAW,CAACN,4BAA4B,IACxCK,aAAa,IACbH,MAAM,CAACF,4BAA4B;EAErCE,MAAM,GAAG;IACP,GAAGA,MAAM;IACT,GAAGI,WAAW;IACdN;EACF,CAAC;AACH;AAEO,SAASO,iBAAiBA,CAACC,MAA+B,EAAE;EACjEN,MAAM,GAAG;IACP,GAAGA,MAAM;IACT,GAAGM;EACL,CAAC;AACH;AAEO,SAASC,eAAeA,CAAA,EAAG;EAChCP,MAAM,GAAG;IAAE,GAAGJ;EAAc,CAAC;AAC/B;AAEO,SAASY,SAASA,CAAA,EAAG;EAC1B,OAAOR,MAAM;AACf", "ignoreList": []}