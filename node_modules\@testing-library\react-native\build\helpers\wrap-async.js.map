{"version": 3, "file": "wrap-async.js", "names": ["_act", "_interopRequireWildcard", "require", "_flushMicroTasks", "_reactVersions", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "wrapAsync", "callback", "checkReactVersionAtLeast", "previousActEnvironment", "getIsReactActEnvironment", "setReactActEnvironment", "result", "flushMicroTasksLegacy", "act"], "sources": ["../../src/helpers/wrap-async.ts"], "sourcesContent": ["/* istanbul ignore file */\n\nimport act, { getIsReactActEnvironment, setReactActEnvironment } from '../act';\nimport { flushMicroTasksLegacy } from '../flush-micro-tasks';\nimport { checkReactVersionAtLeast } from '../react-versions';\n\n/**\n * Run given async callback with temporarily disabled `act` environment and flushes microtasks queue.\n *\n * @param callback Async callback to run\n * @returns Result of the callback\n */\nexport async function wrapAsync<Result>(callback: () => Promise<Result>): Promise<Result> {\n  if (checkReactVersionAtLeast(18, 0)) {\n    const previousActEnvironment = getIsReactActEnvironment();\n    setReactActEnvironment(false);\n\n    try {\n      const result = await callback();\n      // Flush the microtask queue before restoring the `act` environment\n      await flushMicroTasksLegacy();\n      return result;\n    } finally {\n      setReactActEnvironment(previousActEnvironment);\n    }\n  }\n\n  if (!checkReactVersionAtLeast(16, 9)) {\n    return callback();\n  }\n\n  // Wrapping with act for react version 16.9 to 17.x\n  let result: Result;\n  await act(async () => {\n    result = await callback();\n  });\n\n  // Either we have result or `callback` threw error\n  return result!;\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,IAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,gBAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAF,OAAA;AAA6D,SAAAG,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAL,wBAAAK,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAJ7D;;AAMA;AACA;AACA;AACA;AACA;AACA;AACO,eAAeW,SAASA,CAASC,QAA+B,EAAmB;EACxF,IAAI,IAAAC,uCAAwB,EAAC,EAAE,EAAE,CAAC,CAAC,EAAE;IACnC,MAAMC,sBAAsB,GAAG,IAAAC,6BAAwB,EAAC,CAAC;IACzD,IAAAC,2BAAsB,EAAC,KAAK,CAAC;IAE7B,IAAI;MACF,MAAMC,MAAM,GAAG,MAAML,QAAQ,CAAC,CAAC;MAC/B;MACA,MAAM,IAAAM,sCAAqB,EAAC,CAAC;MAC7B,OAAOD,MAAM;IACf,CAAC,SAAS;MACR,IAAAD,2BAAsB,EAACF,sBAAsB,CAAC;IAChD;EACF;EAEA,IAAI,CAAC,IAAAD,uCAAwB,EAAC,EAAE,EAAE,CAAC,CAAC,EAAE;IACpC,OAAOD,QAAQ,CAAC,CAAC;EACnB;;EAEA;EACA,IAAIK,MAAc;EAClB,MAAM,IAAAE,YAAG,EAAC,YAAY;IACpBF,MAAM,GAAG,MAAML,QAAQ,CAAC,CAAC;EAC3B,CAAC,CAAC;;EAEF;EACA,OAAOK,MAAM;AACf", "ignoreList": []}