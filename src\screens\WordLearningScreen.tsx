import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ActivityIndicator,
  StyleSheet,
  PanResponder,
  Dimensions,
  Platform
} from 'react-native';
import WordCard from '../components/WordLearning/WordCard';
import PronunciationButton from '../components/WordLearning/PronunciationButton';
import ActionButtons from '../components/WordLearning/ActionButtons';
import { wordService } from '../services/wordService';
import { Word, UserWordProgress } from '../types/wordLearning';

const WordLearningScreen: React.FC = () => {
  const [words, setWords] = useState<Word[]>([]);
  const [currentWordIndex, setCurrentWordIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userProgress, setUserProgress] = useState<UserWordProgress[]>([]);

  useEffect(() => {
    const fetchWords = async () => {
      try {
        setLoading(true);
        const fetchedWords = await wordService.fetchWords();
        setWords(fetchedWords);
        // Initialize user progress for fetched words
        setUserProgress(fetchedWords.map(word => ({
          wordId: word.id,
          isFavorited: false,
          isMastered: false,
          lastReviewedAt: Date.now(),
          reviewCount: 0,
        })));
      } catch (err) {
        setError('Failed to load words.');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };
    fetchWords();
  }, []);

  // Add keyboard support for web
  useEffect(() => {
    if (Platform.OS === 'web') {
      const handleKeyPress = (event: KeyboardEvent) => {
        if (words.length === 0) return;

        switch (event.key) {
          case 'ArrowLeft':
            setCurrentWordIndex((prevIndex) => (prevIndex - 1 + words.length) % words.length);
            break;
          case 'ArrowRight':
            setCurrentWordIndex((prevIndex) => (prevIndex + 1) % words.length);
            break;
          case ' ': // Spacebar to flip card
            event.preventDefault();
            // This would trigger card flip if we had that state
            break;
        }
      };

      document.addEventListener('keydown', handleKeyPress);
      return () => document.removeEventListener('keydown', handleKeyPress);
    }
  }, [words.length]);

  const currentWord = words[currentWordIndex];
  const currentWordProgress = userProgress.find(p => p.wordId === currentWord?.id);

  const handleFavoriteToggle = async (wordId: string) => {
    const updatedProgress = userProgress.map(p =>
      p.wordId === wordId ? { ...p, isFavorited: !p.isFavorited } : p
    );
    setUserProgress(updatedProgress);
    // Optimistically update UI, then call service
    const progress = updatedProgress.find(p => p.wordId === wordId);
    if (progress) {
      await wordService.updateFavoriteStatus(wordId, progress.isFavorited);
    }
  };

  const handleMarkToggle = async (wordId: string) => {
    const updatedProgress = userProgress.map(p =>
      p.wordId === wordId ? { ...p, isMastered: !p.isMastered } : p
    );
    setUserProgress(updatedProgress);
    // Optimistically update UI, then call service
    const progress = updatedProgress.find(p => p.wordId === wordId);
    if (progress) {
      await wordService.updateMasteredStatus(wordId, progress.isMastered);
    }
  };

  const panResponder = PanResponder.create({
    onMoveShouldSetPanResponder: (evt, gestureState) => {
      return Math.abs(gestureState.dx) > 20;
    },
    onPanResponderRelease: (evt, gestureState) => {
      if (gestureState.dx > 50) {
        // Swipe right - previous word
        if (words.length > 0) {
          setCurrentWordIndex((prevIndex) => (prevIndex - 1 + words.length) % words.length);
        }
      } else if (gestureState.dx < -50) {
        // Swipe left - next word
        if (words.length > 0) {
          setCurrentWordIndex((prevIndex) => (prevIndex + 1) % words.length);
        }
      }
    },
  });

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <ActivityIndicator size="large" color="#0000ff" testID="spinner" />
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.errorText}>{error}</Text>
      </View>
    );
  }

  if (!currentWord) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.normalText}>No words to display.</Text>
      </View>
    );
  }

  return (
    <View style={styles.container} {...panResponder.panHandlers} testID="swipeable-area">
      <View style={styles.contentContainer}>
        <WordCard word={currentWord} />
        <View style={styles.buttonContainer}>
          <PronunciationButton pronunciationUrl={currentWord.pronunciation} />
          <ActionButtons
            wordId={currentWord.id}
            isFavorited={currentWordProgress?.isFavorited || false}
            isMastered={currentWordProgress?.isMastered || false}
            onFavoriteToggle={handleFavoriteToggle}
            onMarkToggle={handleMarkToggle}
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    ...(Platform.OS === 'web' && {
      minHeight: '100vh',
      width: '100%',
    }),
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    ...(Platform.OS === 'web' && {
      minHeight: '100vh',
    }),
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    ...(Platform.OS === 'web' && {
      maxWidth: 800,
      margin: '0 auto',
      padding: 40,
    }),
  },
  buttonContainer: {
    flexDirection: 'row',
    marginTop: 32,
    gap: 16,
    ...(Platform.OS === 'web' && {
      justifyContent: 'center',
      flexWrap: 'wrap',
    }),
  },
  errorText: {
    color: '#dc2626',
    fontSize: 16,
    textAlign: 'center',
  },
  normalText: {
    fontSize: 16,
    color: '#374151',
    textAlign: 'center',
  },
});

export default WordLearningScreen;