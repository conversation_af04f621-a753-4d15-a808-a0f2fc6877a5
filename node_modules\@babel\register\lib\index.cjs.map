{"version": 3, "names": ["exports", "module", "register", "apply", "arguments", "__esModule", "node", "require", "default", "Object", "assign"], "sources": ["../src/index.cts"], "sourcesContent": ["\"use strict\";\n\n/**\n * This file wraps the compiled ES6 module implementation of register so\n * that it can be used both from a standard CommonJS environment, and also\n * from a compiled Babel import.\n */\n\nif (USE_ESM) {\n  module.exports = require(\"./experimental-worker.cjs\");\n} else if (process.env.BABEL_8_BREAKING) {\n  module.exports = require(\"./experimental-worker.cjs\");\n} else {\n  exports = module.exports = function () {\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    return register.apply(this, arguments);\n  };\n  exports.__esModule = true;\n\n  const node = require(\"./nodeWrapper.cjs\");\n  const register = node.default;\n\n  Object.assign(exports, node);\n}\n"], "mappings": "AAAA,YAAY;AAYL;EACLA,OAAO,GAAGC,MAAM,CAACD,OAAO,GAAG,YAAY;IAErC,OAAOE,QAAQ,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EACxC,CAAC;EACDJ,OAAO,CAACK,UAAU,GAAG,IAAI;EAEzB,MAAMC,IAAI,GAAGC,OAAO,CAAC,mBAAmB,CAAC;EACzC,MAAML,QAAQ,GAAGI,IAAI,CAACE,OAAO;EAE7BC,MAAM,CAACC,MAAM,CAACV,OAAO,EAAEM,IAAI,CAAC;AAC9B", "ignoreList": []}