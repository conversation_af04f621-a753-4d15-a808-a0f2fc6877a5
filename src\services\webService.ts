import { Platform } from 'react-native';

class WebService {
  // Web-specific audio playback
  async playAudio(url: string): Promise<void> {
    if (Platform.OS !== 'web') {
      console.warn('WebService.playAudio is only available on web platform');
      return;
    }

    try {
      const audio = new Audio(url);
      audio.preload = 'auto';
      
      return new Promise((resolve, reject) => {
        audio.onloadeddata = () => {
          audio.play()
            .then(() => resolve())
            .catch(reject);
        };
        
        audio.onerror = () => {
          reject(new Error('Failed to load audio'));
        };
        
        // Fallback for immediate play attempt
        audio.play()
          .then(() => resolve())
          .catch(() => {
            // Audio might need user interaction first
            console.log('Audio play requires user interaction');
            resolve();
          });
      });
    } catch (error) {
      console.error('Error playing audio:', error);
      throw error;
    }
  }

  // Web-specific local storage
  setLocalStorage(key: string, value: any): void {
    if (Platform.OS !== 'web' || typeof localStorage === 'undefined') {
      console.warn('localStorage is not available');
      return;
    }

    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error('Error setting localStorage:', error);
    }
  }

  getLocalStorage(key: string): any | null {
    if (Platform.OS !== 'web' || typeof localStorage === 'undefined') {
      console.warn('localStorage is not available');
      return null;
    }

    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : null;
    } catch (error) {
      console.error('Error getting localStorage:', error);
      return null;
    }
  }

  // Web-specific clipboard functionality
  async copyToClipboard(text: string): Promise<boolean> {
    if (Platform.OS !== 'web') {
      console.warn('Clipboard API is only available on web platform');
      return false;
    }

    try {
      if (navigator.clipboard && navigator.clipboard.writeText) {
        await navigator.clipboard.writeText(text);
        return true;
      } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);
        return successful;
      }
    } catch (error) {
      console.error('Error copying to clipboard:', error);
      return false;
    }
  }

  // Web-specific fullscreen functionality
  async toggleFullscreen(): Promise<boolean> {
    if (Platform.OS !== 'web') {
      console.warn('Fullscreen API is only available on web platform');
      return false;
    }

    try {
      if (!document.fullscreenElement) {
        await document.documentElement.requestFullscreen();
        return true;
      } else {
        await document.exitFullscreen();
        return false;
      }
    } catch (error) {
      console.error('Error toggling fullscreen:', error);
      return false;
    }
  }

  // Web-specific keyboard shortcuts
  setupKeyboardShortcuts(callbacks: {
    onPrevious?: () => void;
    onNext?: () => void;
    onFlip?: () => void;
    onFavorite?: () => void;
    onMastered?: () => void;
  }): () => void {
    if (Platform.OS !== 'web') {
      console.warn('Keyboard shortcuts are only available on web platform');
      return () => {};
    }

    const handleKeyDown = (event: KeyboardEvent) => {
      // Prevent default behavior for our shortcuts
      switch (event.key) {
        case 'ArrowLeft':
          event.preventDefault();
          callbacks.onPrevious?.();
          break;
        case 'ArrowRight':
          event.preventDefault();
          callbacks.onNext?.();
          break;
        case ' ':
          event.preventDefault();
          callbacks.onFlip?.();
          break;
        case 'f':
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault();
            callbacks.onFavorite?.();
          }
          break;
        case 'm':
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault();
            callbacks.onMastered?.();
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    
    // Return cleanup function
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }
}

export const webService = new WebService();
