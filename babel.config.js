module.exports = function(api) {
  api.cache(true);

  return {
    presets: ['module:metro-react-native-babel-preset'],
    plugins: [
      'react-native-web/babel',
      'react-native-reanimated/plugin', // This should be last
    ],
    env: {
      web: {
        presets: [
          ['@babel/preset-env', { targets: { browsers: ['last 2 versions'] } }],
          '@babel/preset-react',
          '@babel/preset-typescript',
        ],
        plugins: [
          'react-native-web/babel',
        ],
      },
    },
  };
};