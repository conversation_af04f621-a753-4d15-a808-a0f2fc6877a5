{"version": 3, "file": "fire-event.js", "names": ["_act", "_interopRequireDefault", "require", "_componentTree", "_hostComponentNames", "_pointerEvents", "_textInput", "_nativeState", "e", "__esModule", "default", "isTouchResponder", "element", "isHostElement", "Boolean", "props", "onStartShouldSetResponder", "isHostTextInput", "eventsAffectedByPointerEventsProp", "Set", "textInputEventsIgnoringEditableProp", "isEventEnabled", "eventName", "nearestTouchResponder", "isTextInputEditable", "has", "isPointerEventEnabled", "touchStart", "touchMove", "onMoveShouldSetResponder", "undefined", "findEventHandler", "touchResponder", "handler", "getEventHandler", "parent", "eventHandlerName", "getEventHandlerName", "char<PERSON>t", "toUpperCase", "slice", "fireEvent", "data", "isElementMounted", "setNativeStateIfNeeded", "returnValue", "act", "press", "changeText", "scroll", "_default", "exports", "scrollEventNames", "value", "nativeState", "valueForElement", "set", "isHostScrollView", "contentOffset", "tryGetContentOffset", "contentOffsetForElement", "event", "nativeEvent", "x", "y", "Number", "isFinite"], "sources": ["../src/fire-event.ts"], "sourcesContent": ["import { ReactTestInstance } from 'react-test-renderer';\nimport {\n  ViewProps,\n  TextProps,\n  TextInputProps,\n  PressableProps,\n  ScrollViewProps,\n} from 'react-native';\nimport act from './act';\nimport { isElementMounted, isHostElement } from './helpers/component-tree';\nimport { isHostScrollView, isHostTextInput } from './helpers/host-component-names';\nimport { isPointerEventEnabled } from './helpers/pointer-events';\nimport { isTextInputEditable } from './helpers/text-input';\nimport { Point, StringWithAutocomplete } from './types';\nimport { nativeState } from './native-state';\n\ntype EventHandler = (...args: unknown[]) => unknown;\n\nexport function isTouchResponder(element: ReactTestInstance) {\n  if (!isHostElement(element)) {\n    return false;\n  }\n\n  return Boolean(element.props.onStartShouldSetResponder) || isHostTextInput(element);\n}\n\n/**\n * List of events affected by `pointerEvents` prop.\n *\n * Note: `fireEvent` is accepting both `press` and `onPress` for event names,\n * so we need cover both forms.\n */\nconst eventsAffectedByPointerEventsProp = new Set(['press', 'onPress']);\n\n/**\n * List of `TextInput` events not affected by `editable` prop.\n *\n * Note: `fireEvent` is accepting both `press` and `onPress` for event names,\n * so we need cover both forms.\n */\nconst textInputEventsIgnoringEditableProp = new Set([\n  'contentSizeChange',\n  'onContentSizeChange',\n  'layout',\n  'onLayout',\n  'scroll',\n  'onScroll',\n]);\n\nexport function isEventEnabled(\n  element: ReactTestInstance,\n  eventName: string,\n  nearestTouchResponder?: ReactTestInstance,\n) {\n  if (isHostTextInput(nearestTouchResponder)) {\n    return (\n      isTextInputEditable(nearestTouchResponder) ||\n      textInputEventsIgnoringEditableProp.has(eventName)\n    );\n  }\n\n  if (eventsAffectedByPointerEventsProp.has(eventName) && !isPointerEventEnabled(element)) {\n    return false;\n  }\n\n  const touchStart = nearestTouchResponder?.props.onStartShouldSetResponder?.();\n  const touchMove = nearestTouchResponder?.props.onMoveShouldSetResponder?.();\n  if (touchStart || touchMove) {\n    return true;\n  }\n\n  return touchStart === undefined && touchMove === undefined;\n}\n\nfunction findEventHandler(\n  element: ReactTestInstance,\n  eventName: string,\n  nearestTouchResponder?: ReactTestInstance,\n): EventHandler | null {\n  const touchResponder = isTouchResponder(element) ? element : nearestTouchResponder;\n\n  const handler = getEventHandler(element, eventName);\n  if (handler && isEventEnabled(element, eventName, touchResponder)) return handler;\n\n  // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n  if (element.parent === null || element.parent.parent === null) {\n    return null;\n  }\n\n  return findEventHandler(element.parent, eventName, touchResponder);\n}\n\nfunction getEventHandler(element: ReactTestInstance, eventName: string) {\n  const eventHandlerName = getEventHandlerName(eventName);\n  if (typeof element.props[eventHandlerName] === 'function') {\n    return element.props[eventHandlerName];\n  }\n\n  if (typeof element.props[eventName] === 'function') {\n    return element.props[eventName];\n  }\n\n  return undefined;\n}\n\nfunction getEventHandlerName(eventName: string) {\n  return `on${eventName.charAt(0).toUpperCase()}${eventName.slice(1)}`;\n}\n\n// String union type of keys of T that start with on, stripped of 'on'\ntype EventNameExtractor<T> = keyof {\n  [K in keyof T as K extends `on${infer Rest}` ? Uncapitalize<Rest> : never]: T[K];\n};\n\ntype EventName = StringWithAutocomplete<\n  | EventNameExtractor<ViewProps>\n  | EventNameExtractor<TextProps>\n  | EventNameExtractor<TextInputProps>\n  | EventNameExtractor<PressableProps>\n  | EventNameExtractor<ScrollViewProps>\n>;\n\nfunction fireEvent(element: ReactTestInstance, eventName: EventName, ...data: unknown[]) {\n  if (!isElementMounted(element)) {\n    return;\n  }\n\n  setNativeStateIfNeeded(element, eventName, data[0]);\n\n  const handler = findEventHandler(element, eventName);\n  if (!handler) {\n    return;\n  }\n\n  let returnValue;\n  void act(() => {\n    returnValue = handler(...data);\n  });\n\n  return returnValue;\n}\n\nfireEvent.press = (element: ReactTestInstance, ...data: unknown[]) =>\n  fireEvent(element, 'press', ...data);\n\nfireEvent.changeText = (element: ReactTestInstance, ...data: unknown[]) =>\n  fireEvent(element, 'changeText', ...data);\n\nfireEvent.scroll = (element: ReactTestInstance, ...data: unknown[]) =>\n  fireEvent(element, 'scroll', ...data);\n\nexport default fireEvent;\n\nconst scrollEventNames = new Set([\n  'scroll',\n  'scrollBeginDrag',\n  'scrollEndDrag',\n  'momentumScrollBegin',\n  'momentumScrollEnd',\n]);\n\nfunction setNativeStateIfNeeded(element: ReactTestInstance, eventName: string, value: unknown) {\n  if (\n    eventName === 'changeText' &&\n    typeof value === 'string' &&\n    isHostTextInput(element) &&\n    isTextInputEditable(element)\n  ) {\n    nativeState.valueForElement.set(element, value);\n  }\n\n  if (scrollEventNames.has(eventName) && isHostScrollView(element)) {\n    const contentOffset = tryGetContentOffset(value);\n    if (contentOffset) {\n      nativeState.contentOffsetForElement.set(element, contentOffset);\n    }\n  }\n}\n\nfunction tryGetContentOffset(event: unknown): Point | null {\n  try {\n    // @ts-expect-error: try to extract contentOffset from the event value\n    const contentOffset = event?.nativeEvent?.contentOffset;\n    const x = contentOffset?.x;\n    const y = contentOffset?.y;\n    if (typeof x === 'number' || typeof y === 'number') {\n      return {\n        x: Number.isFinite(x) ? x : 0,\n        y: Number.isFinite(y) ? y : 0,\n      };\n    }\n  } catch {\n    // Do nothing\n  }\n\n  return null;\n}\n"], "mappings": ";;;;;;;;AAQA,IAAAA,IAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,cAAA,GAAAD,OAAA;AACA,IAAAE,mBAAA,GAAAF,OAAA;AACA,IAAAG,cAAA,GAAAH,OAAA;AACA,IAAAI,UAAA,GAAAJ,OAAA;AAEA,IAAAK,YAAA,GAAAL,OAAA;AAA6C,SAAAD,uBAAAO,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAItC,SAASG,gBAAgBA,CAACC,OAA0B,EAAE;EAC3D,IAAI,CAAC,IAAAC,4BAAa,EAACD,OAAO,CAAC,EAAE;IAC3B,OAAO,KAAK;EACd;EAEA,OAAOE,OAAO,CAACF,OAAO,CAACG,KAAK,CAACC,yBAAyB,CAAC,IAAI,IAAAC,mCAAe,EAACL,OAAO,CAAC;AACrF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMM,iCAAiC,GAAG,IAAIC,GAAG,CAAC,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;;AAEvE;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,mCAAmC,GAAG,IAAID,GAAG,CAAC,CAClD,mBAAmB,EACnB,qBAAqB,EACrB,QAAQ,EACR,UAAU,EACV,QAAQ,EACR,UAAU,CACX,CAAC;AAEK,SAASE,cAAcA,CAC5BT,OAA0B,EAC1BU,SAAiB,EACjBC,qBAAyC,EACzC;EACA,IAAI,IAAAN,mCAAe,EAACM,qBAAqB,CAAC,EAAE;IAC1C,OACE,IAAAC,8BAAmB,EAACD,qBAAqB,CAAC,IAC1CH,mCAAmC,CAACK,GAAG,CAACH,SAAS,CAAC;EAEtD;EAEA,IAAIJ,iCAAiC,CAACO,GAAG,CAACH,SAAS,CAAC,IAAI,CAAC,IAAAI,oCAAqB,EAACd,OAAO,CAAC,EAAE;IACvF,OAAO,KAAK;EACd;EAEA,MAAMe,UAAU,GAAGJ,qBAAqB,EAAER,KAAK,CAACC,yBAAyB,GAAG,CAAC;EAC7E,MAAMY,SAAS,GAAGL,qBAAqB,EAAER,KAAK,CAACc,wBAAwB,GAAG,CAAC;EAC3E,IAAIF,UAAU,IAAIC,SAAS,EAAE;IAC3B,OAAO,IAAI;EACb;EAEA,OAAOD,UAAU,KAAKG,SAAS,IAAIF,SAAS,KAAKE,SAAS;AAC5D;AAEA,SAASC,gBAAgBA,CACvBnB,OAA0B,EAC1BU,SAAiB,EACjBC,qBAAyC,EACpB;EACrB,MAAMS,cAAc,GAAGrB,gBAAgB,CAACC,OAAO,CAAC,GAAGA,OAAO,GAAGW,qBAAqB;EAElF,MAAMU,OAAO,GAAGC,eAAe,CAACtB,OAAO,EAAEU,SAAS,CAAC;EACnD,IAAIW,OAAO,IAAIZ,cAAc,CAACT,OAAO,EAAEU,SAAS,EAAEU,cAAc,CAAC,EAAE,OAAOC,OAAO;;EAEjF;EACA,IAAIrB,OAAO,CAACuB,MAAM,KAAK,IAAI,IAAIvB,OAAO,CAACuB,MAAM,CAACA,MAAM,KAAK,IAAI,EAAE;IAC7D,OAAO,IAAI;EACb;EAEA,OAAOJ,gBAAgB,CAACnB,OAAO,CAACuB,MAAM,EAAEb,SAAS,EAAEU,cAAc,CAAC;AACpE;AAEA,SAASE,eAAeA,CAACtB,OAA0B,EAAEU,SAAiB,EAAE;EACtE,MAAMc,gBAAgB,GAAGC,mBAAmB,CAACf,SAAS,CAAC;EACvD,IAAI,OAAOV,OAAO,CAACG,KAAK,CAACqB,gBAAgB,CAAC,KAAK,UAAU,EAAE;IACzD,OAAOxB,OAAO,CAACG,KAAK,CAACqB,gBAAgB,CAAC;EACxC;EAEA,IAAI,OAAOxB,OAAO,CAACG,KAAK,CAACO,SAAS,CAAC,KAAK,UAAU,EAAE;IAClD,OAAOV,OAAO,CAACG,KAAK,CAACO,SAAS,CAAC;EACjC;EAEA,OAAOQ,SAAS;AAClB;AAEA,SAASO,mBAAmBA,CAACf,SAAiB,EAAE;EAC9C,OAAO,KAAKA,SAAS,CAACgB,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGjB,SAAS,CAACkB,KAAK,CAAC,CAAC,CAAC,EAAE;AACtE;;AAEA;;AAaA,SAASC,SAASA,CAAC7B,OAA0B,EAAEU,SAAoB,EAAE,GAAGoB,IAAe,EAAE;EACvF,IAAI,CAAC,IAAAC,+BAAgB,EAAC/B,OAAO,CAAC,EAAE;IAC9B;EACF;EAEAgC,sBAAsB,CAAChC,OAAO,EAAEU,SAAS,EAAEoB,IAAI,CAAC,CAAC,CAAC,CAAC;EAEnD,MAAMT,OAAO,GAAGF,gBAAgB,CAACnB,OAAO,EAAEU,SAAS,CAAC;EACpD,IAAI,CAACW,OAAO,EAAE;IACZ;EACF;EAEA,IAAIY,WAAW;EACf,KAAK,IAAAC,YAAG,EAAC,MAAM;IACbD,WAAW,GAAGZ,OAAO,CAAC,GAAGS,IAAI,CAAC;EAChC,CAAC,CAAC;EAEF,OAAOG,WAAW;AACpB;AAEAJ,SAAS,CAACM,KAAK,GAAG,CAACnC,OAA0B,EAAE,GAAG8B,IAAe,KAC/DD,SAAS,CAAC7B,OAAO,EAAE,OAAO,EAAE,GAAG8B,IAAI,CAAC;AAEtCD,SAAS,CAACO,UAAU,GAAG,CAACpC,OAA0B,EAAE,GAAG8B,IAAe,KACpED,SAAS,CAAC7B,OAAO,EAAE,YAAY,EAAE,GAAG8B,IAAI,CAAC;AAE3CD,SAAS,CAACQ,MAAM,GAAG,CAACrC,OAA0B,EAAE,GAAG8B,IAAe,KAChED,SAAS,CAAC7B,OAAO,EAAE,QAAQ,EAAE,GAAG8B,IAAI,CAAC;AAAC,IAAAQ,QAAA,GAAAC,OAAA,CAAAzC,OAAA,GAEzB+B,SAAS;AAExB,MAAMW,gBAAgB,GAAG,IAAIjC,GAAG,CAAC,CAC/B,QAAQ,EACR,iBAAiB,EACjB,eAAe,EACf,qBAAqB,EACrB,mBAAmB,CACpB,CAAC;AAEF,SAASyB,sBAAsBA,CAAChC,OAA0B,EAAEU,SAAiB,EAAE+B,KAAc,EAAE;EAC7F,IACE/B,SAAS,KAAK,YAAY,IAC1B,OAAO+B,KAAK,KAAK,QAAQ,IACzB,IAAApC,mCAAe,EAACL,OAAO,CAAC,IACxB,IAAAY,8BAAmB,EAACZ,OAAO,CAAC,EAC5B;IACA0C,wBAAW,CAACC,eAAe,CAACC,GAAG,CAAC5C,OAAO,EAAEyC,KAAK,CAAC;EACjD;EAEA,IAAID,gBAAgB,CAAC3B,GAAG,CAACH,SAAS,CAAC,IAAI,IAAAmC,oCAAgB,EAAC7C,OAAO,CAAC,EAAE;IAChE,MAAM8C,aAAa,GAAGC,mBAAmB,CAACN,KAAK,CAAC;IAChD,IAAIK,aAAa,EAAE;MACjBJ,wBAAW,CAACM,uBAAuB,CAACJ,GAAG,CAAC5C,OAAO,EAAE8C,aAAa,CAAC;IACjE;EACF;AACF;AAEA,SAASC,mBAAmBA,CAACE,KAAc,EAAgB;EACzD,IAAI;IACF;IACA,MAAMH,aAAa,GAAGG,KAAK,EAAEC,WAAW,EAAEJ,aAAa;IACvD,MAAMK,CAAC,GAAGL,aAAa,EAAEK,CAAC;IAC1B,MAAMC,CAAC,GAAGN,aAAa,EAAEM,CAAC;IAC1B,IAAI,OAAOD,CAAC,KAAK,QAAQ,IAAI,OAAOC,CAAC,KAAK,QAAQ,EAAE;MAClD,OAAO;QACLD,CAAC,EAAEE,MAAM,CAACC,QAAQ,CAACH,CAAC,CAAC,GAAGA,CAAC,GAAG,CAAC;QAC7BC,CAAC,EAAEC,MAAM,CAACC,QAAQ,CAACF,CAAC,CAAC,GAAGA,CAAC,GAAG;MAC9B,CAAC;IACH;EACF,CAAC,CAAC,MAAM;IACN;EAAA;EAGF,OAAO,IAAI;AACb", "ignoreList": []}