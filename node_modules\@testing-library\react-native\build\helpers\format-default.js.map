{"version": 3, "file": "format-default.js", "names": ["_reactNative", "require", "_object", "propsToDisplay", "defaultMapProps", "props", "result", "styles", "StyleSheet", "flatten", "style", "styleToDisplay", "extractStyle", "undefined", "accessibilityState", "removeUndefinedKeys", "accessibilityValue", "for<PERSON>ach", "propName", "display", "opacity", "has<PERSON>ny<PERSON><PERSON><PERSON>", "Object", "keys", "length"], "sources": ["../../src/helpers/format-default.ts"], "sourcesContent": ["import { StyleSheet, ViewStyle } from 'react-native';\nimport { removeUndefinedKeys } from './object';\n\nconst propsToDisplay = [\n  'accessible',\n  'accessibilityElementsHidden',\n  'accessibilityHint',\n  'accessibilityLabel',\n  'accessibilityLabelledBy',\n  'accessibilityRole',\n  'accessibilityViewIsModal',\n  'alt',\n  'aria-busy',\n  'aria-checked',\n  'aria-disabled',\n  'aria-expanded',\n  'aria-hidden',\n  'aria-label',\n  'aria-labelledby',\n  'aria-modal',\n  'aria-selected',\n  'aria-valuemax',\n  'aria-valuemin',\n  'aria-valuenow',\n  'aria-valuetext',\n  'defaultValue',\n  'importantForAccessibility',\n  'nativeID',\n  'placeholder',\n  'role',\n  'testID',\n  'title',\n  'value',\n] as const;\n\n/**\n * Preserve props that are helpful in diagnosing test failures, while stripping rest\n */\nexport function defaultMapProps(props: Record<string, unknown>): Record<string, unknown> {\n  const result: Record<string, unknown> = {};\n\n  const styles = StyleSheet.flatten(props.style as ViewStyle);\n  const styleToDisplay = extractStyle(styles);\n  if (styleToDisplay !== undefined) {\n    result.style = styleToDisplay;\n  }\n\n  const accessibilityState = removeUndefinedKeys(props.accessibilityState);\n  if (accessibilityState !== undefined) {\n    result.accessibilityState = accessibilityState;\n  }\n\n  const accessibilityValue = removeUndefinedKeys(props.accessibilityValue);\n  if (accessibilityValue !== undefined) {\n    result.accessibilityValue = accessibilityValue;\n  }\n\n  propsToDisplay.forEach((propName) => {\n    if (propName in props) {\n      result[propName] = props[propName];\n    }\n  });\n\n  return result;\n}\n\nfunction extractStyle(style: ViewStyle | undefined) {\n  if (style == null) {\n    return undefined;\n  }\n\n  const result: Record<string, unknown> = {};\n  if (style.display === 'none') {\n    result.display = 'none';\n  }\n\n  if (style.opacity === 0) {\n    result.opacity = 0;\n  }\n\n  const hasAnyKeys = Object.keys(result).length > 0;\n  return hasAnyKeys ? result : undefined;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AAEA,MAAME,cAAc,GAAG,CACrB,YAAY,EACZ,6BAA6B,EAC7B,mBAAmB,EACnB,oBAAoB,EACpB,yBAAyB,EACzB,mBAAmB,EACnB,0BAA0B,EAC1B,KAAK,EACL,WAAW,EACX,cAAc,EACd,eAAe,EACf,eAAe,EACf,aAAa,EACb,YAAY,EACZ,iBAAiB,EACjB,YAAY,EACZ,eAAe,EACf,eAAe,EACf,eAAe,EACf,eAAe,EACf,gBAAgB,EAChB,cAAc,EACd,2BAA2B,EAC3B,UAAU,EACV,aAAa,EACb,MAAM,EACN,QAAQ,EACR,OAAO,EACP,OAAO,CACC;;AAEV;AACA;AACA;AACO,SAASC,eAAeA,CAACC,KAA8B,EAA2B;EACvF,MAAMC,MAA+B,GAAG,CAAC,CAAC;EAE1C,MAAMC,MAAM,GAAGC,uBAAU,CAACC,OAAO,CAACJ,KAAK,CAACK,KAAkB,CAAC;EAC3D,MAAMC,cAAc,GAAGC,YAAY,CAACL,MAAM,CAAC;EAC3C,IAAII,cAAc,KAAKE,SAAS,EAAE;IAChCP,MAAM,CAACI,KAAK,GAAGC,cAAc;EAC/B;EAEA,MAAMG,kBAAkB,GAAG,IAAAC,2BAAmB,EAACV,KAAK,CAACS,kBAAkB,CAAC;EACxE,IAAIA,kBAAkB,KAAKD,SAAS,EAAE;IACpCP,MAAM,CAACQ,kBAAkB,GAAGA,kBAAkB;EAChD;EAEA,MAAME,kBAAkB,GAAG,IAAAD,2BAAmB,EAACV,KAAK,CAACW,kBAAkB,CAAC;EACxE,IAAIA,kBAAkB,KAAKH,SAAS,EAAE;IACpCP,MAAM,CAACU,kBAAkB,GAAGA,kBAAkB;EAChD;EAEAb,cAAc,CAACc,OAAO,CAAEC,QAAQ,IAAK;IACnC,IAAIA,QAAQ,IAAIb,KAAK,EAAE;MACrBC,MAAM,CAACY,QAAQ,CAAC,GAAGb,KAAK,CAACa,QAAQ,CAAC;IACpC;EACF,CAAC,CAAC;EAEF,OAAOZ,MAAM;AACf;AAEA,SAASM,YAAYA,CAACF,KAA4B,EAAE;EAClD,IAAIA,KAAK,IAAI,IAAI,EAAE;IACjB,OAAOG,SAAS;EAClB;EAEA,MAAMP,MAA+B,GAAG,CAAC,CAAC;EAC1C,IAAII,KAAK,CAACS,OAAO,KAAK,MAAM,EAAE;IAC5Bb,MAAM,CAACa,OAAO,GAAG,MAAM;EACzB;EAEA,IAAIT,KAAK,CAACU,OAAO,KAAK,CAAC,EAAE;IACvBd,MAAM,CAACc,OAAO,GAAG,CAAC;EACpB;EAEA,MAAMC,UAAU,GAAGC,MAAM,CAACC,IAAI,CAACjB,MAAM,CAAC,CAACkB,MAAM,GAAG,CAAC;EACjD,OAAOH,UAAU,GAAGf,MAAM,GAAGO,SAAS;AACxC", "ignoreList": []}