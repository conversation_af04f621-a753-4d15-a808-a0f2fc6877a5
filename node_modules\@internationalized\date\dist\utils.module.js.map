{"mappings": "AAAA;;;;;;;;;;CAUC,GAMM,SAAS,0CAAI,MAAc,EAAE,SAAiB;IACnD,OAAO,SAAS,YAAY,KAAK,KAAK,CAAC,SAAS;AAClD", "sources": ["packages/@internationalized/date/src/utils.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nexport type Mutable<T> = {\n  -readonly[P in keyof T]: T[P]\n};\n\nexport function mod(amount: number, numerator: number): number {\n  return amount - numerator * Math.floor(amount / numerator);\n}\n"], "names": [], "version": 3, "file": "utils.module.js.map"}